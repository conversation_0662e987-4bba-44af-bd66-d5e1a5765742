import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuController } from '@ionic/angular';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { UtilserviceService } from '../services/utilservice.service';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faEnvelope, faGrip, faListCheck } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-add-configuration',
  templateUrl: './add-configuration.page.html',
  styleUrls: ['./add-configuration.page.scss'],
})
export class AddConfigurationPage implements OnInit {

  constructor(public router: Router,
    public utilityService: UtilserviceService,
    public menu: MenuController,
    public dataService: DataService,
    public faIconLibrary: FaIconLibrary,
    public helpService: HelpService) {
      this.faIconLibrary.addIcons(faBars, faEnvelope, faGrip, faListCheck)
     }

  ngOnInit() {
  }

  navigateToObservation(page) {
    this.utilityService.setMeasurementEditMode(false);
    switch (page) {
      case 'End':
        this.router.navigateByUrl('/end-image');
        break;
      case 'Chafe':
        this.router.navigateByUrl('/chafe-image');
        break;
      case 'Splice':
        this.router.navigateByUrl('/splice-image');
        break;
      case 'Other':
        this.router.navigateByUrl('/other-image');
        break;
    }
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }
}
