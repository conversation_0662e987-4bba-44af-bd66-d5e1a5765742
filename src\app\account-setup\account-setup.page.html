<ion-header>
  <ion-toolbar>
    <ion-title>Setup Account</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>

  <div style="width:100% !important">
    <ion-card class="card-style-multiple">
      <ion-card-content class="card-content-style">
        <p style="margin-top: 8px;">Account</p>
        <div style="display: inline-flex;width: 100%;">
          <ion-item (click)="presentModal('ACCOUNT')" no-lines text-wrap tappable
            *ngIf="accountsList && accountsList.length > 0" class="ion-item-generic-style" mode="ios"
            style="width: 100%;">
            <div
              style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
              *ngIf="!selectedAccount || selectedAccount.NAME == ''" class="drop-down-arrow  value-field">{{ 'Select
              Account'
              }}</div>
            <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
              *ngIf="selectedAccount && selectedAccount.NAME != ''" class="value-field">{{selectedAccount.NAME}}</div>
            <ion-note item-right style="display:inline-flex">
              <p class="drop-down-arrow">
                <fa-icon class="icon-style" icon="sort-down"></fa-icon>
              </p>
            </ion-note>
          </ion-item>
          <div style="padding: 10px;">
            <fa-icon class="icon-style-help" icon="times" (click)="clearAccount()"></fa-icon>
          </div>
        </div>


        <p style="margin-top: 8px;" *ngIf="selectedAccount && selectedAccount.NAME != ''">Asset</p>
        <div style="display: inline-flex;width: 100%;" *ngIf="selectedAccount && selectedAccount.NAME != ''">
          <ion-item (click)="presentModal('ASSET')" no-lines text-wrap tappable *ngIf="assetList && assetList.length> 0"
            class="ion-item-generic-style" mode="ios" style="width: 100%;">
            <div
              style="width:100%; text-align: left; background-color: white !important; font-size: 14px;color: darkgray;"
              *ngIf="!selectedAsset || selectedAsset == ''" class="drop-down-arrow  value-field">{{ 'Select Asset'}}
            </div>

            <div style="width:100%; text-align: left; background-color: white !important; font-size: 14px;"
              *ngIf="selectedAsset && selectedAsset.NAME != ''" class="value-field">{{selectedAsset.NAME}}
            </div>
            <ion-note item-right style="display:inline-flex">
              <p class="drop-down-arrow">
                <fa-icon class="icon-style" icon="sort-down" style="color: darkgrey;"></fa-icon>
              </p>
            </ion-note>
          </ion-item>
          <div style="padding: 10px;" *ngIf="selectedAccount && selectedAccount.NAME != ''">
            <fa-icon class="icon-style-help" icon="times" (click)="clearAsset()"></fa-icon>
          </div>
        </div>
        <ion-button (click)="showCustomizationAlert()" color="primary" expand="block" [disabled]="(selectedAccount == undefined || selectedAccount == '' || selectedAccount.NAME == '') || (selectedAsset == undefined || selectedAsset == '' || selectedAsset.NAME == '')">Download</ion-button>
      </ion-card-content>
    </ion-card>
  </div>

</ion-content>