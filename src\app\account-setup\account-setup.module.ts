import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { AccountSetupPageRoutingModule } from './account-setup-routing.module';

import { AccountSetupPage } from './account-setup.page';
import { TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    AccountSetupPageRoutingModule,
    TranslateModule,
    FontAwesomeModule
  ],
  declarations: [AccountSetupPage]
})
export class AccountSetupPageModule {}
