// * this script is to modify the nodemodules files
const fs = require('fs');
const path = require('path');

const scriptDirectory = __dirname;
const webgpuFilePath = path.join(scriptDirectory, '../node_modules/@webgpu/types/dist/index.d.ts');
const tensorflowFilePath = path.join(scriptDirectory, '../node_modules/@tensorflow/tfjs-core/dist/hash_util.d.ts');
const searchToken = 'PredefinedColorSpace';
const newValue = '"display-p3" | "srgb"';
const newLineToAdd = '/// <reference types="long" />'

// Read the content of the file
fs.readFile(webgpuFilePath, 'utf8', (err, data) => {
  if (err) {
    console.error(`Error reading the file: ${err}`);
    return;
  }

  // Replace the token with the new value
  const modifiedData = data.replace(new RegExp(searchToken, 'g'), newValue);
  // Write the modified content back to the file
  fs.writeFile(webgpuFilePath, modifiedData, 'utf8', (writeErr) => {
    if (writeErr) {
      console.error(`Error updating @webgpu/types/dist/index.d.ts file : ${writeErr}`);
      return;
    }
    console.log(`@webgpu/types/dist/index.d.ts updated successfully during postinstall. \n`);
  });
});

// Read the content of the file
fs.readFile(tensorflowFilePath, 'utf8', (err, data) => {
  if (err) {
    console.error(`Error reading the file @webgpu/types/dist/index.d.ts : ${err}`);
    return;
  }

  // Check if the new line is already present
  if (data.includes(newLineToAdd)) {
    console.log('@tensorflow/tfjs-core/dist/hash_util.d.ts file is already updated...skipping the updation \n');
    return;
  }

  // Split the content into an array of lines
  const lines = data.split('\n');

  // Add the new line as the second line (index 1)
  lines.splice(1, 0, newLineToAdd);

  // Join the lines back into a single string
  const modifiedData = lines.join('\n');

  // Write the modified content back to the file
  fs.writeFile(tensorflowFilePath, modifiedData, 'utf8', (writeErr) => {
    if (writeErr) {
      console.error(`Error updating @tensorflow/tfjs-core/dist/hash_util.d.ts file : ${writeErr}`);
      return;
    }
    console.log(`@tensorflow/tfjs-core/dist/hash_util.d.ts file updated successfully during postinstall. \n`);
  });
});