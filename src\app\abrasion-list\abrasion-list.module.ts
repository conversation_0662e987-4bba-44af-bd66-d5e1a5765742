import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Routes, RouterModule } from '@angular/router';

import { IonicModule } from '@ionic/angular';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';

import { AbrasionListPage } from './abrasion-list.page';

const routes: Routes = [
  {
    path: '',
    component: AbrasionListPage
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    FontAwesomeModule,
    TranslateModule,
    RouterModule.forChild(routes)
  ],
  declarations: [AbrasionListPage]
})
export class AbrasionListPageModule {}
