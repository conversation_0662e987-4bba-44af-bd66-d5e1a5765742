import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { HelpService } from 'src/app/services/help.service';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { LmdService } from 'src/app/services/lmd.service';
import { UtilserviceService } from 'src/app/services/utilservice.service';
import { MenuController } from '@ionic/angular';
import { DataService } from 'src/app/services/data.service';
import { AlertService } from 'src/app/services/alert.service';
import * as moment from 'moment';
import { UserPreferenceService } from 'src/app/services/user-preference.service';

@Component({
  selector: 'app-configuration-port-details',
  templateUrl: './configuration-port-details.page.html',
  styleUrls: ['./configuration-port-details.page.scss'],
  encapsulation: ViewEncapsulation.Emulated
})
export class ConfigurationPortDetailsPage implements OnInit {

  existingConfigurations: any;
  tempArray: any = [];
  selectedAssetActivity: any;
  selectedAsset: any;
  selectedWinch: any = [];
  isWorkboat: boolean = false;

  constructor(
    public translate: TranslateService,
    public menu: MenuController,
    public helpService: HelpService,
    public lmdService: LmdService,
    public dataService: DataService,
    public router: Router,
    public alertService: AlertService,
    public userPreferenceService: UserPreferenceService,
    public utilityService: UtilserviceService,
    public unviredSDK: UnviredCordovaSDK
  ) { }

  ngOnInit() {
    this.selectedAssetActivity = this.lmdService.getSelectedAssetActivity()
    this.selectedAsset = this.lmdService.getSelectedAsset()
    this.getWinches();
  }

  saveLMD() {
    this.lmdService.setSelectedAssetActivity(this.selectedAssetActivity);
    this.lmdService.setSelectedAsset(this.selectedAsset)
    this.utilityService.setSelectedAssetActivity(this.selectedAssetActivity)
    this.router.navigate(['configuration-winches']);
  }

  async getWinches() {
    this.isWorkboat = false;
    var tempData = JSON.parse(this.selectedAssetActivity.LMD_DATA)
    var query = `SELECT *
                    FROM EXISTING_LMD_HEADER
                    WHERE ASSET_ID = '${this.selectedAsset.ID}'
                        AND PORT_COUNTRY = '${tempData.portCountry}'
                        AND PORT_NAME = '${tempData.portName}' COLLATE NOCASE
                ORDER BY  EXISTING_LMD_HEADER.PORT_ENTERED_DATE DESC`;
    // if(tempData.shipSide != '' && tempData.shipSide != undefined && tempData.shipSide != null) {
    //   query = query + `AND SHIP_SIDE = '${tempData.shipSide}' `
    // }
    // if(tempData.terminalType != '' && tempData.terminalType != undefined && tempData.terminalType != null) {
    //   query = query + `AND SHELTERED_EXPOSED_PORT = '${tempData.terminalType}' `
    // }
    // query = query + `group by EXISTING_LMD_HEADER.PORT_ENTERED_DATE, EXISTING_LMD_HEADER.SHELTERED_EXPOSED_PORT, EXISTING_LMD_HEADER.SHIP_SIDE, EXISTING_LMD_HEADER.PORT_NAME`
    
    let equipmentRes = await this.unviredSDK.dbExecuteStatement(query)
    if (equipmentRes.type == ResultType.success) {
      if (equipmentRes.data.length > 0) {
        for (var i = 0; i < equipmentRes.data.length; i++) {
          if(equipmentRes.data[i].PORT_ENTERED_DATE != '' && equipmentRes.data[i].PORT_ENTERED_DATE != undefined && equipmentRes.data[i].PORT_ENTERED_DATE != null) {
            equipmentRes.data[i].LOCAL_PORT_ENTERED_DATE = moment.utc(equipmentRes.data[i].PORT_ENTERED_DATE).local().format("YYYY-MM-DD HH:mm:ss A");
          }
        }
        var temp = []; 
        var selectedIndustry = await this.userPreferenceService.getUserPreference('industry');
        if (selectedIndustry != undefined && selectedIndustry != "undefined" && selectedIndustry != null && selectedIndustry != '' && selectedIndustry != '""') {
          selectedIndustry = JSON.parse(selectedIndustry)
          if(selectedIndustry.ID.includes("Workboat")) {            
            this.isWorkboat = true;
            for(var ind = 0; ind < equipmentRes.data.length; ind++) {
              if(equipmentRes.data[ind].WINCH == '' || equipmentRes.data[ind].WINCH == undefined || equipmentRes.data[ind].WINCH == null) {
                temp.push(equipmentRes.data[ind])
              }
            }
          }
        }
        if(this.isWorkboat == true) {
          this.existingConfigurations = temp;
        } else {
          this.existingConfigurations = equipmentRes.data;
        }
        await this.getCurrentLMDWinches();
      } else {
        this.existingConfigurations = []
        await this.getCurrentLMDWinches()
      }
    } else {
      this.unviredSDK.logError("create-inspection", "filterCertificate", "Error while getting error from db" + JSON.stringify(this.existingConfigurations))
    }
  }

  async getCurrentLMDWinches() {
    this.isWorkboat = false;
    var tempData = JSON.parse(this.selectedAssetActivity.LMD_DATA)
    var query = `Select LMD.LMD_ID,
                LMD.LMD_NAME,
                json_extract(LMD.LMD_DATA, '$.WINCHTYPE') AS WINCHTYPE,
                json_extract(LMD.LMD_DATA, '$.WINCHES[0].SELECTED_TAIL.CERTIFICATE_NUM') AS CERTIFICTAE_NUM,
                json_extract(LMD.LMD_DATA, '$.terminalType') AS SHELTERED_EXPOSED_PORT,
                json_extract(LMD.LMD_DATA, '$.winches[0].MAINLINE') AS MAINLINE,
                json_extract(LMD.LMD_DATA, '$.winches[0].EQUIP_CERT') AS EQUIP_CERT,
                json_extract(LMD.LMD_DATA, '$.asset') as ASSET_ID,
                json_extract(LMD.LMD_DATA, '$.winches[0].MAINLINE') AS CERT_ID,
                json_extract(LMD.LMD_DATA, '$.winches[0].EQUIP_RPS') AS RPS,
                json_extract(LMD.LMD_DATA, '$.portName') AS PORT_NAME,
                json_extract(LMD.LMD_DATA, '$.portCountry') AS PORT_COUNTRY,
                json_extract(LMD.LMD_DATA, '$.shipSide') AS SHIP_SIDE,
                json_extract(LMD.LMD_DATA, '$.winches[0].EQUIP_ID') AS EQUIPMENT,
                json_extract(LMD.LMD_DATA, '$.winches[0].EQUIP_NAME') AS WINCH,
                json_extract(LMD.LMD_DATA, '$.winches[0].EQUIP_NAME') AS LINE_POSITION,
                json_extract(LMD.LMD_DATA, '$.winches[0].SELECTED_TAIL.RPS') AS TAIL_RPS,
                json_extract(LMD.LMD_DATA, '$.allFastUTC') AS PORT_ENTERED_DATE,
                json_extract(LMD.LMD_DATA, '$.allLetGoUTC') AS PORT_DEPARTED_DATE,
                json_extract(LMD.LMD_DATA, '$.winches[0].SELECTED_TAIL') AS SELECTED_TAIL,
                json_extract(LMD.LMD_DATA, '$.winches[0].EQUIP_CERT_NAME') AS EQUIP_CERT_NAME,
                LMD.LMD_DATA AS LMD_DATA
            from LMD_HEADER LMD
            WHERE LMD_DATA LIKE '%"asset":"${this.selectedAsset.ID}"%'
              AND LMD_DATA LIKE '%"portName":"${tempData.portName}"%' COLLATE NOCASE
              AND LMD_DATA LIKE '%"portCountry":"${tempData.portCountry}"%'
            ORDER BY PORT_ENTERED_DATE DESC`;

    // if(tempData.shipSide != '' && tempData.shipSide != undefined && tempData.shipSide != null) {
    //   query = query + `AND json_extract(LMD_DATA,'$.shipSide') = '${ tempData.shipSide }' `
    // }

    // if( tempData.terminalType != '' && tempData.terminalType != undefined && tempData.terminalType != null) {
    //   query = query + `AND json_extract(LMD_DATA,'$.terminalType') = '${ tempData.terminalType }' `
    // }
    // query = query + `group by json_extract(LMD_DATA,'$.terminalType') , json_extract(LMD_DATA,'$.shipSide') , json_extract(LMD_DATA, '$.portName')`

      let equipmentRes = await this.unviredSDK.dbExecuteStatement(query)
      if (equipmentRes.type == ResultType.success) {
        if (equipmentRes.data.length > 0) {
          for (var i = 0; i < equipmentRes.data.length; i++) {
            if(equipmentRes.data[i].PORT_ENTERED_DATE != '' && equipmentRes.data[i].PORT_ENTERED_DATE != undefined && equipmentRes.data[i].PORT_ENTERED_DATE != null) {
              equipmentRes.data[i].LOCAL_PORT_ENTERED_DATE = moment.utc(equipmentRes.data[i].PORT_ENTERED_DATE).local().format("YYYY-MM-DD HH:mm:ss A");
            }
          }
          var temp = []; 
          var selectedIndustry = await this.userPreferenceService.getUserPreference('industry');
          if (selectedIndustry != undefined && selectedIndustry != "undefined" && selectedIndustry != null && selectedIndustry != '' && selectedIndustry != '""') {
            selectedIndustry = JSON.parse(selectedIndustry)
            if(selectedIndustry.ID.includes("Workboat")) {
              this.isWorkboat = true;
              for(var ind = 0; ind < equipmentRes.data.length; ind++) {
                if(equipmentRes.data[ind].WINCH == '' || equipmentRes.data[ind].WINCH == undefined || equipmentRes.data[ind].WINCH == null) {
                  temp.push(equipmentRes.data[ind])
                }
              }
            }
          }
          if(this.isWorkboat == true) {
            this.existingConfigurations.concat(temp);
          } else {
            // this.existingConfigurations =  this.existingConfigurations.concat(equipmentRes.data)
            equipmentRes.data.forEach(ele=>{
              if(this.existingConfigurations.length>0) {
                if(this.existingConfigurations.some(el=>(el.ASSET_ID==ele.ASSET_ID && el.PORT_COUNTRY==ele.PORT_COUNTRY && el.PORT_NAME==ele.PORT_NAME && moment(el.PORT_DEPARTED_DATE).isSame(ele.PORT_DEPARTED_DATE) && moment(el.PORT_ENTERED_DATE).isSame(ele.PORT_ENTERED_DATE)))) {
                  // do something
                  if(this.existingConfigurations.some(el=>(el.ASSET_ID==ele.ASSET_ID && el.PORT_COUNTRY==ele.PORT_COUNTRY && el.PORT_NAME==ele.PORT_NAME && moment(el.PORT_DEPARTED_DATE).isSame(ele.PORT_DEPARTED_DATE) && moment(el.PORT_ENTERED_DATE).isSame(ele.PORT_ENTERED_DATE)))) {
                    // do something
                    if(ele) {

                    }
                  }
                } else {
                  this.existingConfigurations.push(ele);
                }
              } else {
                this.existingConfigurations.push(ele);
              }
            })
          }
        }
      } else {
        this.unviredSDK.logError("create-inspection", "filterCertificate", "Error while getting error from db" + JSON.stringify(this.existingConfigurations))
      }
  }

  checkSectionIsOpen(i) {
    const ind = this.tempArray.indexOf(i, 0);
    return (ind > -1);
  }


  async expandedItem(index, item) {
    const ind = this.tempArray.indexOf(index, 0);
    if (ind > -1) {
      this.tempArray.splice(ind, 1);
    } else {
      this.tempArray.push(index);
    }
  }

  getArray(index) {
    var temp = this.tempArray.indexOf(index, 0).toString();
    // return this.tempList[this.tempArray.indexOf(index, 0)]
  }

  async copyConfig(event:Event,index:number) {
    event.stopPropagation();
    // var query = `Select * , C.NAME as CERT_NAME from EXISTING_LMD_HEADER, ROPE_PRODUCT_SPEC_HEADER as R, CERTIFICATE_HEADER AS C where R.ID = EXISTING_LMD_HEADER.TAIL_RPS AND R.CERT = C.ID AND PORT_ENTERED_DATE = '${this.existingConfigurations[index].PORT_ENTERED_DATE}' AND PORT_NAME = '${this.existingConfigurations[index].PORT_NAME}' AND PORT_COUNTRY = '${this.existingConfigurations[index].PORT_COUNTRY}' `;
    // if(this.existingConfigurations[index].SHELTERED_EXPOSED_PORT != '' && this.existingConfigurations[index].SHELTERED_EXPOSED_PORT != undefined && this.existingConfigurations[index].SHELTERED_EXPOSED_PORT != null) {
    //   query = query + `AND SHELTERED_EXPOSED_PORT = '${this.existingConfigurations[index].SHELTERED_EXPOSED_PORT}' `
    // }
    // if(this.existingConfigurations[index].SHIP_SIDE != '' && this.existingConfigurations[index].SHIP_SIDE != undefined && this.existingConfigurations[index].SHIP_SIDE != null) {
    //   query = query + `AND SHIP_SIDE = '${this.existingConfigurations[index].SHIP_SIDE}'`
    // }
    this.alertService.present()
    if(this.existingConfigurations[index].WINCH != '' && this.existingConfigurations[index].WINCH != undefined && this.existingConfigurations[index].WINCH != null) {
      await this.getEquipment(index)
      this.lmdService.setSelectedAssetActivity(this.selectedAssetActivity);
      this.lmdService.setSelectedAsset(this.selectedAsset)
      this.utilityService.setSelectedAssetActivity(this.selectedAssetActivity);
      this.lmdService.setFromConfig(true)
      this.router.navigate(['configuration-winches']);
      await this.alertService.dismiss()
    } else {
      await this.getCertificates(index)
      this.lmdService.setSelectedAssetActivity(this.selectedAssetActivity);
      this.lmdService.setSelectedAsset(this.selectedAsset)
      this.utilityService.setSelectedAssetActivity(this.selectedAssetActivity);
      this.lmdService.setFromConfig(true)
      this.router.navigate(['configuration-winches']);
      await this.alertService.dismiss()
    }
  }

  async getEquipment(index) {
    this.selectedWinch = [];
    var query = ` SELECT C.NAME AS CERT_NAME, C.ID AS CERT_ID,R.EQUIP_DETAILS,R.CERT,LMD.TAIL_RPS,E.EQUIP_ID,E.EQUIP_NAME
                  FROM EXISTING_LMD_HEADER AS LMD, ROPE_PRODUCT_SPEC_HEADER AS R, CERTIFICATE_HEADER AS C, EQUIPMENT_HEADER AS E
                  WHERE C.ID = '${this.existingConfigurations[index].CERT_ID}'
                        AND E.EQUIP_NAME = '${this.existingConfigurations[index].WINCH}'
                        AND E.ASSET_ID = '${this.existingConfigurations[index].ASSET_ID}'
                        AND PORT_ENTERED_DATE = '${this.existingConfigurations[index].PORT_ENTERED_DATE}'
                        AND PORT_NAME = '${this.existingConfigurations[index].PORT_NAME}'`;
    if (this.existingConfigurations[index].SHELTERED_EXPOSED_PORT != '' && this.existingConfigurations[index].SHELTERED_EXPOSED_PORT != undefined && this.existingConfigurations[index].SHELTERED_EXPOSED_PORT != null) {
      query = query + `AND SHELTERED_EXPOSED_PORT = '${this.existingConfigurations[index].SHELTERED_EXPOSED_PORT}' `
    }
    if (this.existingConfigurations[index].SHIP_SIDE != '' && this.existingConfigurations[index].SHIP_SIDE != undefined && this.existingConfigurations[index].SHIP_SIDE != null) {
      query = query + `AND LMD.SHIP_SIDE = '${this.existingConfigurations[index].SHIP_SIDE}' `
    }

    if(this.existingConfigurations[index].TAIL_RPS!=null || this.existingConfigurations[index].TAIL_RPS=='') {
      query += ' AND R.ID = LMD.TAIL_RPS'
    }

    query = query + ' group by EQUIP_NAME'
    var res = await this.unviredSDK.dbExecuteStatement(query);
    // var res = await this.unviredSDK.dbSelect("EQUIPMENT_HEADER", "EQUIP_ID = '" + this.existingConfigurations[index].EQUIPMENT + "'")

    if (res.type == ResultType.success) {
      if (res.data.length > 0) {
        for(var rx = 0; rx < res.data.length; rx++) {
          var endInUse = ''
          var mainline = ''
          var mainlineId = ''
          let rpsQUery = `SELECT R.END_IN_USE,R.APPLICATION,C.NAME,C.CERTIFICATE_NUM
                          FROM ROPE_PRODUCT_SPEC_HEADER AS R, CERTIFICATE_HEADER AS C
                          WHERE R.APPLICATION = 'Mainline'
                              AND C.ID = '${res.data[rx].CERT_ID}'
                              AND R.CERT = C.CERTIFICATE_NUM`;

          // if(this.existingConfigurations[index].TAIL_RPS) {
          //   rpsQUery += 'AND C.ID = "${this.existingConfigurations[index].TAIL_RPS}"'
          // }

          var rpsItem = await this.unviredSDK.dbExecuteStatement(rpsQUery)
          if (rpsItem.type == ResultType.success) {
            if (rpsItem.data.length > 0) {
              endInUse = rpsItem.data[0].END_IN_USE
            } 
            // else {
            //   this.getCurrentEquipemt(index);
            // }
            for (var x = 0; x < rpsItem.data.length; x++) {
              if (rpsItem.data[x].APPLICATION == 'Mainline' || rpsItem.data[x].APPLICATION == 'mainline') {
                mainline = rpsItem.data[x].NAME
                mainlineId = rpsItem.data[x].CERTIFICATE_NUM
              }
            }
          }

          let tailRpsQuery = `SELECT ROPE_PRODUCT_SPEC_HEADER.CURRENT_LENGTH_IN_METER,
                                  ROPE_PRODUCT_SPEC_HEADER.APPLICATION,
                                  ROPE_PRODUCT_SPEC_HEADER.CERT,
                                  CERTIFICATE_HEADER.NAME,
                                  CERTIFICATE_HEADER.CERTIFICATE_NUM,
                                  CERTIFICATE_HEADER.RPS
                              FROM ROPE_PRODUCT_SPEC_HEADER,
                                  CERTIFICATE_HEADER
                              where ROPE_PRODUCT_SPEC_HEADER.EQUIP_DETAILS = '${res.data[rx].EQUIP_ID}'
                                  AND CERTIFICATE_HEADER.ID = ROPE_PRODUCT_SPEC_HEADER.CERT
                                  AND ROPE_PRODUCT_SPEC_HEADER.APPLICATION = 'Mooring Tails'`
          
          let tailRes = await this.unviredSDK.dbExecuteStatement(tailRpsQuery)
          if (tailRes.type == ResultType.success) {
            var tailLength = []
            if (tailRes.data.length > 0) {
              tailLength = tailRes.data;
            } else {
              tailLength = []
            }

            let obj = {
              'CURRENT_LENGTH_IN_METER' : '',
              'CERT':'',
              'NAME':'',
              'APPLICATION':'',
              'CERTIFICATE_NUM':'',
              'RPS':''
            }

            let tailObj = JSON.parse(JSON.stringify(obj));
            tailObj.CURRENT_LENGTH_IN_METER = 'No Tail Required';
            tailLength.push(tailObj);

            let otherObj =  JSON.parse(JSON.stringify(obj));
            otherObj.CURRENT_LENGTH_IN_METER = 'Other Tail';
            tailLength.push(otherObj);
            
            var tempObj:any = {
              "EQUIP_ID": res.data[rx].EQUIP_ID,
              "EQUIP_NAME": res.data[rx].EQUIP_NAME,
              "EQUIP_CERT": '',
              "EQUIP_CERT_NAME": '',
              "EQUIP_RPS": '',
              "TAIL_LENGTH": '',
              "ESTIMATE_LINE_LENGTH": '',
              "TAIL_LIST": tailLength,
              "SELECTED_TAIL": '',
              "END_IN_USE": '', //TODO has to be filled based on the END_IN_USE coming from server
              "AVERAGE_LOAD": '',
              "PEAK_LOAD": '',
              "MAINLINE": mainlineId,
              'MAINLINE_NAME': mainline,
              "NOTES": '',
            }
            
            if(tailLength.length == 1) {
              tempObj.SELECTED_TAIL = tailLength[0]
              tempObj.TAIL_LENGTH = tailLength[0].CURRENT_LENGTH_IN_METER
              tempObj.EQUIP_CERT_NAME = tailLength[0].NAME
              tempObj.EQUIP_CERT = tailLength[0].CERTIFICATE_NUM
              tempObj.EQUIP_RPS = tailLength[0].RPS
            } else {
              for(var ti = 0; ti < tailLength.length; ti++){
                if(this.existingConfigurations[index].TAIL_RPS==null || this.existingConfigurations[index].TAIL_RPS=='') {
                  let tailObject = tempObj.TAIL_LIST.find(ele=>ele.CURRENT_LENGTH_IN_METER==="No Tail Required");
                  tempObj.SELECTED_TAIL = tailObject;
                  tempObj.TAIL_LENGTH = tailObject.CURRENT_LENGTH_IN_METER;
                  tempObj.EQUIP_CERT_NAME = tailObject.NAME;
                  tempObj.EQUIP_CERT = tailObject.CERTIFICATE_NUM;
                  tempObj.EQUIP_RPS = tailObject.RPS;
                } else if(tailLength[ti].CURRENT_LENGTH_IN_METER!='Other Tail' && tailLength[ti].RPS == this.existingConfigurations[index].TAIL_RPS) {
                  tempObj.SELECTED_TAIL = tailLength[ti]
                  tempObj.TAIL_LENGTH = tailLength[ti].CURRENT_LENGTH_IN_METER
                  tempObj.EQUIP_CERT_NAME = tailLength[ti].NAME
                  tempObj.EQUIP_CERT = tailLength[ti].CERTIFICATE_NUM
                  tempObj.EQUIP_RPS = tailLength[ti].RPS
                }
              }
            }

            // ! this is to check whether the selected tail is empty and the copied lmd has the TAIL_RPS or not
            if(this.existingConfigurations[index].TAIL_RPS!=null && this.existingConfigurations[index].TAIL_RPS!='' && tempObj.SELECTED_TAIL=="") {
              let tailQuery = `SELECT ROPE_PRODUCT_SPEC_HEADER.CURRENT_LENGTH_IN_METER,
                                  ROPE_PRODUCT_SPEC_HEADER.APPLICATION,
                                  ROPE_PRODUCT_SPEC_HEADER.CERT,
                                  CERTIFICATE_HEADER.NAME,
                                  CERTIFICATE_HEADER.CERTIFICATE_NUM,
                                  CERTIFICATE_HEADER.RPS
                              FROM ROPE_PRODUCT_SPEC_HEADER,
                                  CERTIFICATE_HEADER
                              where ROPE_PRODUCT_SPEC_HEADER.ID = '${this.existingConfigurations[index].TAIL_RPS}'
                                  AND CERTIFICATE_HEADER.ID = ROPE_PRODUCT_SPEC_HEADER.CERT`;
              let tailResult = await this.unviredSDK.dbExecuteStatement(tailQuery)
              if (tailResult.type == ResultType.success) {
                if (tailResult.data.length > 0) {
                  tempObj.SELECTED_TAIL =  JSON.parse(JSON.stringify(tailResult.data[0]));
                  tempObj.SELECTED_TAIL.CURRENT_LENGTH_IN_METER = "";
                  tempObj.TAIL_LIST.push(tempObj.SELECTED_TAIL);

                  tempObj.EQUIP_CERT_NAME = tailResult.data[0].NAME;
                  tempObj.EQUIP_CERT = tailResult.data[0].CERTIFICATE_NUM;
                  tempObj.EQUIP_RPS = tailResult.data[0].RPS;
                  tempObj.TAIL_LENGTH = tailResult.data[0].CURRENT_LENGTH_IN_METER;
                }
              }
            }
          this.selectedWinch.push(tempObj)
            // tempData.winch = this.existingConfigurations[index].winch            
          }
        }
        var tempData = JSON.parse(this.selectedAssetActivity.LMD_DATA)
        tempData.winchType = 'equipment'
        tempData.winches = this.selectedWinch
        this.selectedAssetActivity.LMD_DATA = JSON.stringify(tempData)
        await this.getCurrentEquipemt(index);
        this.alertService.dismiss()
      } else {
        await this.getCurrentEquipemt(index);
      }
      
    }
  }

  async getCurrentEquipemt(index) {
    this.selectedWinch = [];
    var query=` Select json_extract(LMD.LMD_DATA, '$.winches[0].EQUIP_CERT') AS EQUIP_CERT,
                  json_extract(LMD.LMD_DATA, '$.winches[0].SELECTED_TAIL.CERTIFICATE_NUM') AS CERTIFICATENUM,
                  json_extract(LMD.LMD_DATA, '$.winchType') AS WINCHTYPE,
                  json_extract(LMD.LMD_DATA, '$.winches[0].MAINLINE') AS MAINLINE,
                  json_extract(LMD.LMD_DATA, '$.allFastUTC') AS PORT_ENTERED_DATE,
                  json_extract(LMD.LMD_DATA, '$.allLetGoUTC') AS PORT_DEPARTED_DATE,
                  json_extract(LMD_DATA, '$.asset') AS ASSET,
                  json_extract(LMD_DATA, '$.assetName') AS ASSET_NAME,
                  C.NAME as CERT_NAME,
                  json_extract(LMD.LMD_DATA, '$.portName') AS PORT_NAME,
                  json_extract(LMD.LMD_DATA, '$.allFast') AS PORT_ENTERED_DATE,
                  json_extract(LMD.LMD_DATA, '$.winches[0].EQUIP_ID') AS EQUIP_ID,
                  json_extract(LMD.LMD_DATA, '$.terminalType') AS SHELTERED_EXPOSED_PORT,
                  LMD.LMD_DATA AS LMD_DATA,
                  C.*,
                  R.*,
                  E.*
              from LMD_HEADER as LMD,
                ROPE_PRODUCT_SPEC_HEADER as R,
                CERTIFICATE_HEADER AS C,
                EQUIPMENT_HEADER as E
                WHERE 1=1  -- Simplified: get all records and filter in application
                AND R.CERT = C.ID
                AND E.ASSET_ID = '${this.existingConfigurations[index].ASSET_ID}'
                AND LMD.LMD_DATA LIKE '%"allFastUTC":"${this.existingConfigurations[index].PORT_ENTERED_DATE}"%'
                AND LMD_DATA LIKE '%"portName":"${this.existingConfigurations[index].PORT_NAME}"%' `;

      if (this.existingConfigurations[index].SHELTERED_EXPOSED_PORT != '' && this.existingConfigurations[index].SHELTERED_EXPOSED_PORT != undefined && this.existingConfigurations[index].SHELTERED_EXPOSED_PORT != null) {
        query = query + `AND LMD.LMD_DATA LIKE '%"terminalType":"${this.existingConfigurations[index].SHELTERED_EXPOSED_PORT}"%' `
      }
      if (this.existingConfigurations[index].SHIP_SIDE != '' && this.existingConfigurations[index].SHIP_SIDE != undefined && this.existingConfigurations[index].SHIP_SIDE != null) {
        query = query + `AND LMD.LMD_DATA LIKE '%"shipSide":"${this.existingConfigurations[index].SHIP_SIDE}"%' `
      }

      query = query + 'group by EQUIP_NAME'
      var res = await this.unviredSDK.dbExecuteStatement(query);

      if (res.type == ResultType.success) {
        if (res.data.length > 0) {
          for(var rx = 0; rx < res.data.length; rx++) {
            let tailLength = []
            tailLength = JSON.parse(res.data[rx].LMD_DATA).winches[0].TAIL_LIST;

            var tempObj:any = {
              "EQUIP_ID": JSON.parse(res.data[rx].LMD_DATA).winches[0].EQUIP_ID,
              "EQUIP_NAME": JSON.parse(res.data[rx].LMD_DATA).winches[0].EQUIP_NAME,
              "EQUIP_CERT": JSON.parse(res.data[rx].LMD_DATA).winches[0].EQUIP_CERT,
              "EQUIP_CERT_NAME": JSON.parse(res.data[rx].LMD_DATA).winches[0].EQUIP_CERT_NAME,
              "EQUIP_RPS": JSON.parse(res.data[rx].LMD_DATA).winches[0].EQUIP_RPS,
              "TAIL_LENGTH": res.data[rx].CURRENT_LENGTH_IN_METER,
              "ESTIMATE_LINE_LENGTH": '',
              "TAIL_LIST": tailLength,
              "SELECTED_TAIL": JSON.parse(res.data[rx].LMD_DATA).winches[0].SELECTED_TAIL,
              "END_IN_USE": JSON.parse(res.data[rx].LMD_DATA).winches[0].END_IN_USE,
              "AVERAGE_LOAD": '',
              "PEAK_LOAD": '',
              "MAINLINE": JSON.parse(res.data[rx].LMD_DATA).winches[0].MAINLINE,
              'MAINLINE_NAME': JSON.parse(res.data[rx].LMD_DATA).winches[0].MAINLINE_NAME,
              "NOTES": '',
            }

            if(tempObj.SELECTED_TAIL.CURRENT_LENGTH_IN_METER=='Other Tail') {
              let tailQuery = `SELECT ROPE_PRODUCT_SPEC_HEADER.CURRENT_LENGTH_IN_METER,
                                  ROPE_PRODUCT_SPEC_HEADER.APPLICATION,
                                  ROPE_PRODUCT_SPEC_HEADER.CERT,
                                  CERTIFICATE_HEADER.NAME,
                                  CERTIFICATE_HEADER.CERTIFICATE_NUM,
                                  CERTIFICATE_HEADER.RPS
                              FROM ROPE_PRODUCT_SPEC_HEADER,
                                  CERTIFICATE_HEADER
                              where ROPE_PRODUCT_SPEC_HEADER.ID = '${JSON.parse(res.data[rx].LMD_DATA).winches[0].EQUIP_RPS}'
                                  AND CERTIFICATE_HEADER.ID = ROPE_PRODUCT_SPEC_HEADER.CERT`;
              let tailResult = await this.unviredSDK.dbExecuteStatement(tailQuery)
              if (tailResult.type == ResultType.success) {
                if (tailResult.data.length > 0) {
                  tempObj.SELECTED_TAIL =  JSON.parse(JSON.stringify(tailResult.data[0]));
                  tempObj.SELECTED_TAIL.CURRENT_LENGTH_IN_METER = "";
                  tempObj.TAIL_LIST.push(tempObj.SELECTED_TAIL);
                }
              }
            }
            // let tailObject = JSON.parse(JSON.stringify(tempObj));
            this.selectedWinch.push(tempObj);
            tempObj = '';
          }
          
          var tempData = JSON.parse(this.selectedAssetActivity.LMD_DATA)
          tempData.winchType = 'equipment'
          tempData.winches = this.selectedWinch
          this.selectedAssetActivity.LMD_DATA = JSON.stringify(tempData)
          this.alertService.dismiss()
        }}
  }

  async getCertificates(index) {
    // var res = await this.unviredSDK.dbSelect("CERTIFICATE_HEADER", "CERTIFICATE_NUM = '" + this.existingConfigurations[index].CERT_ID + "'") {}
      // let certRes = await this.unviredSDK.dbExecuteStatement(`SELECT CERTIFICATE_HEADER.NAME, CERTIFICATE_HEADER.CERTIFICATE_NUM, CERTIFICATE_HEADER.PRODUCT, CERTIFICATE_HEADER.DIAM, CERTIFICATE_HEADER.RPS, ROPE_PRODUCT_SPEC_HEADER.END_IN_USE, ROPE_PRODUCT_SPEC_HEADER.CURRENT_LENGTH_IN_METER FROM CERTIFICATE_HEADER, ROPE_PRODUCT_SPEC_HEADER WHERE CERTIFICATE_HEADER.CERTIFICATE_NUM = '${this.existingConfigurations[index].CERT_ID}' AND CERTIFICATE_HEADER.RPS = ROPE_PRODUCT_SPEC_HEADER.ID`)
      var query = `Select *, C.NAME as CERT_NAME, C.ID as CERT_ID from EXISTING_LMD_HEADER as LMD, ROPE_PRODUCT_SPEC_HEADER as R, CERTIFICATE_HEADER AS C where R.ID = LMD.TAIL_RPS AND R.CERT = C.ID AND PORT_ENTERED_DATE = '${this.existingConfigurations[index].PORT_ENTERED_DATE}' AND PORT_NAME = '${this.existingConfigurations[index].PORT_NAME}' `
      if (this.existingConfigurations[index].SHELTERED_EXPOSED_PORT != '' && this.existingConfigurations[index].SHELTERED_EXPOSED_PORT != undefined && this.existingConfigurations[index].SHELTERED_EXPOSED_PORT != null) {
        query = query + `AND SHELTERED_EXPOSED_PORT = '${this.existingConfigurations[index].SHELTERED_EXPOSED_PORT}' `
      }
      if (this.existingConfigurations[index].SHIP_SIDE != '' && this.existingConfigurations[index].SHIP_SIDE != undefined && this.existingConfigurations[index].SHIP_SIDE != null) {
        query = query + `AND LMD.SHIP_SIDE = '${this.existingConfigurations[index].SHIP_SIDE}' `
      }
      let certRes = await this.unviredSDK.dbExecuteStatement(query)
      if (certRes.type == ResultType.success) {
        if (certRes.data.length > 0) {
          for(var ci = 0; ci < certRes.data.length; ci++) {
          var temp = {
            "EQUIP_ID": '',
            "EQUIP_NAME": '',
            "EQUIP_CERT": certRes.data[ci].CERT_ID,
            "EQUIP_CERT_NAME": certRes.data[ci].CERT_NAME,
            "EQUIP_RPS": certRes.data[ci].RPS,
            "TAIL_LENGTH": certRes.data[ci].CURRENT_LENGTH_IN_METER,
            "ESTIMATE_LINE_LENGTH": '',
            "TAIL_LIST": [certRes.data[ci]],
            "SELECTED_TAIL": certRes.data[ci],
            "END_IN_USE": certRes.data[ci].END_IN_USE,
            "AVERAGE_LOAD": '',
            "PEAK_LOAD": '',
            "MAINLINE": '',
            "MAINLINE_NAME": '',
            "NOTES": '',
          }
        this.selectedWinch.push(temp)
        }
        var tempData = JSON.parse(this.selectedAssetActivity.LMD_DATA)
        tempData.winchType = 'lines'
        tempData.winches = this.selectedWinch
        this.selectedAssetActivity.LMD_DATA = JSON.stringify(tempData)
        this.alertService.dismiss()
        } else {
          await this.getCurrentCertificates(index);
        }
      }
  }

  async getCurrentCertificates(index) {
    // var query = `Select *, C.NAME as CERT_NAME, C.ID as CERT_ID from EXISTING_LMD_HEADER as LMD, ROPE_PRODUCT_SPEC_HEADER as R, CERTIFICATE_HEADER AS C where R.ID = LMD.TAIL_RPS AND R.CERT = C.ID AND PORT_ENTERED_DATE = '${this.existingConfigurations[index].PORT_ENTERED_DATE}' AND PORT_NAME = '${this.existingConfigurations[index].PORT_NAME}' `
    var query = `Select C.*, R.*, C.NAME as CERT_NAME, C.ID as CERT_ID, json_extract(LMD.LMD_DATA,'$.allFastUTC') AS PORT_ENTERED_DATE, json_extract(LMD.LMD_DATA,'$.allLetGoUTC') AS PORT_DEPARTED_DATE, json_extract(LMD.LMD_DATA,'$.asset') AS ASSET, json_extract(LMD.LMD_DATA,'$.portName') AS PORT_NAME, json_extract(LMD.LMD_DATA,'$.portCountry') AS PORT_COUNTRY, json_extract(LMD.LMD_DATA,'$.shipSide') AS SHIP_SIDE, json_extract(LMD_DATA,'$.terminalType') AS SHELTERED_EXPOSED_PORT from LMD_HEADER as LMD, ROPE_PRODUCT_SPEC_HEADER as R, CERTIFICATE_HEADER AS C where R.ID = json_extract(LMD.LMD_DATA,'$.winches[0].EQUIP_RPS')  AND R.CERT = C.ID AND json_extract(LMD.LMD_DATA,'$.allFastUTC') = '${this.existingConfigurations[index].PORT_ENTERED_DATE}' AND json_extract(LMD.LMD_DATA,'$.portName')='${this.existingConfigurations[index].PORT_NAME}`;
    if (this.existingConfigurations[index].SHELTERED_EXPOSED_PORT != '' && this.existingConfigurations[index].SHELTERED_EXPOSED_PORT != undefined && this.existingConfigurations[index].SHELTERED_EXPOSED_PORT != null) {
        query = query + `AND SHELTERED_EXPOSED_PORT = '${this.existingConfigurations[index].SHELTERED_EXPOSED_PORT}' `
      }
      if (this.existingConfigurations[index].SHIP_SIDE != '' && this.existingConfigurations[index].SHIP_SIDE != undefined && this.existingConfigurations[index].SHIP_SIDE != null) {
        query = query + `AND LMD.SHIP_SIDE = '${this.existingConfigurations[index].SHIP_SIDE}' `
      }
      let certRes = await this.unviredSDK.dbExecuteStatement(query)
      if (certRes.type == ResultType.success) {
        if (certRes.data.length > 0) {
          for(var ci = 0; ci < certRes.data.length; ci++) {
          var temp = {
            "EQUIP_ID": '',
            "EQUIP_NAME": '',
            "EQUIP_CERT": certRes.data[ci].CERT_ID,
            "EQUIP_CERT_NAME": certRes.data[ci].CERT_NAME,
            "EQUIP_RPS": certRes.data[ci].RPS,
            "TAIL_LENGTH": certRes.data[ci].CURRENT_LENGTH_IN_METER,
            "ESTIMATE_LINE_LENGTH": '',
            "TAIL_LIST": [certRes.data[ci]],
            "SELECTED_TAIL": certRes.data[ci],
            "END_IN_USE": certRes.data[ci].END_IN_USE,
            "AVERAGE_LOAD": '',
            "PEAK_LOAD": '',
            "MAINLINE": '',
            "MAINLINE_NAME": '',
            "NOTES": '',
          }
        this.selectedWinch.push(temp)
        }
        var tempData = JSON.parse(this.selectedAssetActivity.LMD_DATA)
        tempData.winchType = 'lines'
        tempData.winches = this.selectedWinch
        this.selectedAssetActivity.LMD_DATA = JSON.stringify(tempData)
        this.alertService.dismiss()
        }
      }
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }
}
