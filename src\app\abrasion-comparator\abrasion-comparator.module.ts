import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { AbrasionComparatorPageRoutingModule } from './abrasion-comparator-routing.module';

import { AbrasionComparatorPage } from './abrasion-comparator.page';
import { TranslateModule } from '@ngx-translate/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    TranslateModule,
    FontAwesomeModule,
    AbrasionComparatorPageRoutingModule
  ],
  declarations: [AbrasionComparatorPage]
})
export class AbrasionComparatorPageModule {}
