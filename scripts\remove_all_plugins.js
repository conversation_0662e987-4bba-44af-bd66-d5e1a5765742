const { exec } = require('child_process');

// Function to remove all plugins
function removeAllPlugins() {
    exec('ionic cordova plugin list', (error, stdout, stderr) => {
        if (error) {
            console.error(`Error: ${error.message}`);
            return;
        }
        if (stderr) {
            console.error(`Command stderr: ${stderr}`);
            return;
        }

        // Extract plugin names from the output
        const pluginNames = stdout.split('\n')
            .map(line => line.split(' ')[0])
            .filter(plugin => plugin !== 'cordova-plugin-whitelist-123'); // Exclude cordova-plugin-whitelist if you want to keep it
        
        // Remove each plugin
        pluginNames.forEach(plugin => {
            exec(`cordova plugin rm ${plugin}`, (error, stdout, stderr) => {
                if (error) {
                    console.error(`Error removing plugin ${plugin}: ${error.message}`);
                    return;
                }
                console.log(`Removed plugin: ${plugin}`);
            });
        });
    });
}

// Call the function to remove all plugins
removeAllPlugins();
