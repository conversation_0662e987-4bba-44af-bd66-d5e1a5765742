@media screen and (min-width:10px) and (max-width:500px) {
    .inspectionHome {
        padding: 0% 15% 0% 15%;
        height: 92%
    }
}

@media screen and (min-width: 501px) {
    .inspectionHome {
        padding: 0% 25% 0% 25%;
        height: 92%
    }
}

.card-md:nth-child(even) {
    float: right;
}

.card-md {
    width: 48%;
    display: inline-block;
    margin: 1px 0px;
    height: 25% !important;
    position: relative;
}

img {
    height: 100%;
}

ion-label {
    position: absolute;
    left: 0;
    bottom: 0;
    color: white;
    background: rgba(0, 0, 0, 0.4);
    width: 100%;
    padding: 5px;
    text-align: center;
    margin: 0 !important;
}




.col8 {
    width: 60%;
}

.col4 {
    width: 33%;
    min-height: 50px
}

.col6 {
    width: 50%;
    min-height: 50px
}

.border-style {
    padding: 1px;
    border-radius: 3px;
    // border: solid black 1px !important;
}

.tile {
    color: white !important;
    background-color: #0057b3 !important;
    text-align: left !important;
    height: 100%;
    line-height: 50px;
}

.card-style {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0) !important;
    margin: 0px !important;
}

.card-header-style {
    font-size: 18px !important;
    padding: 10px 10px 5px 10px !important;

}

.card-content-style {
    padding: 5px 10px 10px 10px !important;
}

// .footer-style {
//     background-color: #00b0ff !important;
//     color: white !important;
//     line-height: 40px !important;
//     min-height: 40px !important;
//     text-align: center !important;
// }

// ion-fab-button {
//     --background:#00b0ff !important;
// }
.card-badge {
    width: auto;
    height: 25px;
    border-radius: 40px;
}