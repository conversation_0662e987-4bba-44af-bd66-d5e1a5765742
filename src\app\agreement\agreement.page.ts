import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-agreement',
  templateUrl: './agreement.page.html',
  styleUrls: ['./agreement.page.scss'],
})
export class AgreementPage implements OnInit {

  confirmationText: string;
  termsAndConditionsText: string;
  termsAndConditionsTitle: string;
  isChecked: any;
  disableLogin: boolean = true;

  constructor(public router: Router,
    public translate: TranslateService,
    private menu:MenuController) {
    this.confirmationText = "Agree Terms And Conditions";
    this.termsAndConditionsText = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
    this.termsAndConditionsTitle = 'Terms and conditions'
  }

  ngOnInit() {
  }

  ionViewWillEnter() {
    this.menu.enable(false,'menu');
  }

  ionvViewWillLeave() {
    this.menu.enable(true,'menu');
  }
 
  login() {
    this.router.navigate(['login']);
  }

  cancel() {
    navigator['app'].exitApp();
  }

  resetPermission(event) {
    console.log(event)
    if (event.detail.checked == true) {
      this.disableLogin = false;
    } else {
      this.disableLogin = true;
    }
  }

}
