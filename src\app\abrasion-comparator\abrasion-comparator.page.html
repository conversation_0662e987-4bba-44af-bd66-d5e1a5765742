<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button style="--color: #0057b3;" defaultHref="/home" (click)='back()'></ion-back-button>
    </ion-buttons>
    <ion-title>Abrasion Comparator</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div  *ngIf="platformId !== 'electron' && device.platform !== 'browser'" style="height:104%">
    <ion-card
      (click)="helpService.helpMode ? '' : internalAbrasion()" class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <ion-label class="card-title card-header-mobile"> {{'Class II HMPE' |translate}} </ion-label>
      <img src="./assets/img/InternalTile.png" style="height: 100%; width: 100%;"/>
      <ion-label class="card-title" style="font-size: 17px;"> {{'Internal Abrasion' |translate}} </ion-label>
    </ion-card>

    <ion-card
      (click)="helpService.helpMode ? '' : externalAbrasion()" class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <ion-label class="card-title card-header-mobile"> {{'Class II HMPE' |translate}} </ion-label>
      <img src="./assets/img/ExternalTile.png" style="height: 100%; width: 100%;"/>
      <ion-label class="card-title" style="font-size: 17px;"> {{'External Abrasion' | translate}} </ion-label>
    </ion-card>

    <ion-card
      (click)="helpService.helpMode ? '' : tenexExternalAbrasion()" class="ion-activatable responsive-card-style">
      <ion-ripple-effect></ion-ripple-effect>
      <ion-label class="card-title card-header-mobile"> {{'Class I Polyester' |translate}} </ion-label>
      <img src="./assets/img/TenexExternalTile.png" style="height: 100%; width: 100%;"/>
      <ion-label class="card-title" style="font-size: 17px;"> {{'Tenex_External_Abrasion' | translate}} </ion-label>
    </ion-card>
  </div>
  
  <div *ngIf="platformId == 'electron' || device.platform == 'browser'">
    <div class="gridCon"> 
      <div class="gridCol">
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : internalAbrasion()">
          <ion-ripple-effect></ion-ripple-effect>
          <ion-label class="card-title card-header-web"> {{'Class II HMPE' |translate}} </ion-label>
          <img src="./assets/img/InternalTile.png" class="imageTag">
          <p class="wrapperLabel">{{'Internal Abrasion' |translate}}</p>
        </div>
      </div>
      <div class="gridCol">
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : externalAbrasion()">
          <ion-ripple-effect></ion-ripple-effect>
          <ion-label class="card-title card-header-web"> {{'Class II HMPE' |translate}} </ion-label>
          <img src="./assets/img/ExternalTile.png" class="imageTag">
          <p class="wrapperLabel">{{'External Abrasion' |translate}}</p>
        </div>
      </div>
      <div class="gridCol">
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : tenexExternalAbrasion()">
          <ion-ripple-effect></ion-ripple-effect>
          <ion-label class="card-title card-header-web"> {{'Class I Polyester' |translate}} </ion-label>
          <img src="./assets/img/TenexExternalTile.png" class="imageTag">
          <p class="wrapperLabel">{{'External Abrasion' |translate}}</p>
        </div>
      </div>
    </div>
  </div>

</ion-content>

<div class="help-block" (click)="helpService.switchMode()" *ngIf="helpService.helpMode"
  style="background: rgba(0,0,0,0.8509803921568627); color: white;  position: fixed; bottom: 0; height: 60px; width: 100%; font-size: 3vh; display: table;">
  <p style="text-align: center; margin: 2vh;"> Tap to exit help mode</p>
</div>
<ion-footer class="footer-style">
  <ion-grid style="text-align:center;">
    <ion-row>
      <ion-col>
        <div (click)="openMenu()">
          <fa-icon class="icon-style" icon="bars"></fa-icon>
        </div>
      </ion-col>
      <!-- <ion-col>
        <div (click)="dataService.navigateToHome()" style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable">
          <ion-ripple-effect></ion-ripple-effect>
          <fa-icon class="icon-style"  icon="home"></fa-icon>
        </div>
      </ion-col> -->
      <ion-col>
        <div (click)="goToLineTracker()" style=" outline-color: rgba(0, 0, 0, 0);" class="ion-activatable">
          <ion-ripple-effect></ion-ripple-effect>
          <fa-icon class="icon-style" icon="list-check"></fa-icon>
        </div>
      </ion-col>
      <ion-col>
        <div (click)="dataService.navigateToInspection()" style=" outline-color: rgba(0, 0, 0, 0);"
          class="ion-activatable">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/icon/Rope_Inspection_ICON_3A.png" class="bottom-bar-image-style fa-fw">
          <!-- <fa-icon class="icon-style" icon="search"></fa-icon> -->
        </div>
      </ion-col>
      <ion-col>
        <div (click)="dataService.navigateToResources()" style=" outline-color: rgba(0, 0, 0, 0);"
          class="ion-activatable">
          <ion-ripple-effect></ion-ripple-effect>
          <fa-icon class="icon-style" icon="grip"></fa-icon>
        </div>
      </ion-col>
      <ion-col>
        <div (click)="dataService.navigateToContact()" style=" outline-color: rgba(0, 0, 0, 0);"
          class="ion-activatable">
          <ion-ripple-effect></ion-ripple-effect>
          <fa-icon class="icon-style" icon="envelope" class="icon-style"></fa-icon>
        </div>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-footer>
