@media screen and (min-width:10px) and (max-width:500px) {
    .inspectionHome {
        padding: 3% 15% 0% 15%;
        height: 95%
    }
}

@media screen and (min-width: 501px) {
    .inspectionHome {
        padding: 3% 25% 0% 25%;
        height: 95%
    }
}

.responsive-card-style {
    height: 31%;
    border-radius: 10px;
    margin: 5px !important
}

@media screen and (min-width: 900px) {
    .responsive-card-style {
        height: 31%;
        border-radius: 10px;
        margin: 5px !important;
        max-width: 390px !important;
        position: relative;
        left: 50%;
        transform: translateX(-50%);

    }
}

.logo {
    position: fixed;
    height: 75px;
    top: -35px;
    right: 45%;

}

.card-md:nth-child(even) {
    float: right;
}

.card-md {
    width: 48%;
    display: inline-block;
    margin: 1px 0px;
    height: 25% !important;
    position: relative;
}

img {
    height: 100%;
}

.card-badge {
    position: absolute;
    right: 3px;
    width: 35px;
    height: 32px;
    border-radius: 30px;
}

ion-label {
    position: absolute;
    left: 0;
    bottom: 0;
    color: white;
    background: rgba(0, 0, 0, 0.4);
    width: 100%;
    padding: 5px;
    text-align: center;
    margin: 0 !important;
}




.gridCon {
    display: -ms-flexbox;
    /* IE10 */
    display: flex;
    -ms-flex-wrap: wrap;
    /* IE10 */
    flex-wrap: wrap;
    padding: 0px 4px 10px 4px;
    width: 55%;
    text-align: center;
    margin: 0 auto;
}

/* Create four equal columns that sits next to each other */
.gridCol {
    -ms-flex: 50%;
    /* IE10 */
    flex: 50%;
    max-width: 50%;
    padding: 0 4px;
}

.imageTag {
    vertical-align: middle;
    width: 100%;
}

.imgWrapper {
    padding: 10px;
    border: 2px solid #f1f1f1;
    border-radius: 10px;
    margin-top: 10px;
    position: relative;
}

.imgWrapper:hover {
    border: 1px solid #0057b3;
}

.wrapperLabel {
    color: white;
    font-weight: 600;
    font-size: 3vh;
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 94%;
    background: rgba(0, 0, 0, 0.4);
    padding: 5px;
    margin: 0;
}

.inProgressCount {
    background: #3c55cf;
    border-bottom-left-radius: 80%;
    width: 15%;
    height: 15%;
    padding: 2em;
    box-sizing: border-box;
    text-align: center;
    position: absolute;
    top: 10px;
    right: 10px;
}

.inProgressCount span {
    color: white;
    font-weight: bold;
    font-size: 1.5em;
    display: inline-block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Responsive layout - makes a two column-layout instead of four columns */
@media screen and (max-width: 800px) {
    .gridCon {
        width: 100%;
    }

    .gridCol {
        -ms-flex: 50%;
        flex: 50%;
        max-width: 50%;
    }
}

/* Responsive layout - makes the two columns stack on top of each other instead of next to each other */
@media screen and (max-width: 400px) {
    .gridCon {
        width: 100%;
    }

    .gridCol {
        -ms-flex: 100%;
        flex: 100%;
        max-width: 100%;
    }
}

.card-header-mobile {
    font-size: 17px;
    font-weight: bold;
    margin:38% 0 !important;
    background:transparent !important;
}

.card-header-web {
    font-size: 3vh;
    font-weight: 600;
    margin:38% 0 !important;
    background:transparent !important;   
}