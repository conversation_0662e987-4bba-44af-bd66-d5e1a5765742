const fs = require('fs');
const path = require('path');

// Define source and destination file mappings
const filesToReplace = [
    {
        src: path.resolve(__dirname, 'cdv-electron-preload.js'),
        dest: path.resolve(__dirname, '../platforms/electron/platform_www/cdv-electron-preload.js')
    },
    {
        src: path.resolve(__dirname, 'cdv-electron-main.js'),
        dest: path.resolve(__dirname, '../platforms/electron/platform_www/cdv-electron-main.js')
    }
];

// Replace each file
filesToReplace.forEach(({ src, dest }) => {
    if (!fs.existsSync(src)) {
        console.error(`Source file not found: ${src}`);
        return;
    }

    // Ensure destination directory exists
    const destDir = path.dirname(dest);
    if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
    }

    // Copy file
    fs.copyFileSync(src, dest);
    console.log(`Replaced: ${dest}`);
});