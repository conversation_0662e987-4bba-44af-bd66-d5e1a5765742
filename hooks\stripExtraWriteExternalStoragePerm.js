const FS = require('fs');
const Path = require('path');

let path = Path.resolve('platforms/android/app/src/main/AndroidManifest.xml');

let manifest = FS.readFileSync(path, {
    encoding: 'utf-8'
});

// Create a set to store unique permissions
const permissionsSet = new Set();

// Use a regular expression to find all <uses-permission> tags
let permissionsPattern = /<uses-permission\s+[^>]*android:name="([^"]+)"[^>]*>/g;

let uniqueManifest = manifest.replace(permissionsPattern, (match, permissionName) => {
    if (!permissionsSet.has(permissionName)) {
        permissionsSet.add(permissionName);
        return match;
    }
    return '';
});

// Remove empty lines left after removing duplicates
uniqueManifest = uniqueManifest.replace(/^\s*$(?:\r\n?|\n)/gm, '');

// Write the updated manifest back to the file
FS.writeFileSync(path, uniqueManifest);