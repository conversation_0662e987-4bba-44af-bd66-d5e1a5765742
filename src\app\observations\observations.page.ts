import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { EmailComposer } from '@awesome-cordova-plugins/email-composer/ngx';
import { UnviredCordovaSDK, NotificationListenerType, ResultType, RequestType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { library } from '@fortawesome/fontawesome-svg-core';
import { faHome, faTh, faEnvelope, faBars, faTasks, faTimes, faInfoCircle, faListCheck, faHouse, faGrip, faCircle, faCircleInfo, faPlus, faTrash, faPencil, faEye, faCheck } from '@fortawesome/free-solid-svg-icons';
import { MenuController, Platform, AlertController, ToastController, NavController, ModalController, PopoverController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { LmdService } from '../services/lmd.service';
import * as moment from 'moment';
import { AppConstant } from 'src/constants/appConstants';
import { INPUT_SEND_REPORT_EMAIL_HEADER } from 'src/models/INPUT_SEND_REPORT_EMAIL';
import { AlertService } from '../services/alert.service';
import { CameraService } from '../services/camera.service';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { UserPreferenceService } from '../services/user-preference.service';
import { UtilserviceService } from '../services/utilservice.service';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { ScreenOrientation } from '@awesome-cordova-plugins/screen-orientation/ngx';
import {MatTooltip} from '@angular/material/tooltip';
import { AdvancedAssistencePage } from '../advanced-assistence/advanced-assistence.page';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { GenericListPage } from '../generic-list/generic-list.page';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { LinearDamagePage } from '../linear-damage/linear-damage.page';
import { AbrasionListPage } from '../abrasion-list/abrasion-list.page';
import { CompressionPage } from '../compression/compression.page';
import { ContaminationPage } from '../contamination/contamination.page';
import { CutsPage } from '../cuts/cuts.page';
import { DiscolorationPage } from '../discoloration/discoloration.page';
import { ExternalAbrasionPage } from '../external-abrasion/external-abrasion.page';
import { InconsistentDiameterPage } from '../inconsistent-diameter/inconsistent-diameter.page';
import { InternalAbrasionPage } from '../internal-abrasion/internal-abrasion.page';
import { KinkingPage } from '../kinking/kinking.page';
import { MeltingPage } from '../melting/melting.page';
import { PartingPage } from '../parting/parting.page';
import { PulledPage } from '../pulled/pulled.page';
import { TwistPage } from '../twist/twist.page';
import { HomePopupPage } from '../home-popup/home-popup.page';
import { PlatformService } from '../services/platform.service';
@Component({
  selector: 'app-observations',
  templateUrl: './observations.page.html',
  styleUrls: ['./observations.page.scss'],
})



export class ObservationsPage implements OnInit {
  end;
  external;
  internal;
  internalImage;
  externalImage;
  exMode = true;
  baselineArray = [];
  id;
  obj;
  type;
  date;
  start;
  filteredArray;
  readFlag;
  dissableAdd = false;
  dbData;
  productName;
  startPoint;
  productConfig;
  certNumber;
  inspectionHeader;
  emptyListInprogress = false;
  emptyListCompleted = false;
  obj1;
  selectedUnit: any;
  syncButtonClicked: boolean = false;
  currentInternalObj: any;
  currentExternalObj: any;
  inspectionName: string;
  toastPresent: boolean = true;
  repToast: any;
  showToastOnLoad: boolean;
  reportDownloaded: boolean = false;
  showPurchaseOption: boolean = false;
  disableAdvancedPayment: boolean = true;
  currentScreenOrientation: any;
  selectedUser: any;
  loggedInUser: any;
  isLoggedInUserEmployee: boolean = false;
  isSampleGridOpen: boolean = true;
  isSpecimanGridOpen: boolean = true
  readOnly:boolean = false;
  selectedIndustry: any;
  isUtility: boolean = false;
  platformId: string = this.platformService.getPlatformId();

  labEmployeeObservationList: any[] = [];
  labEmployeeSpecimenslist: any[] = [];
  @ViewChild("tooltip") tooltip: MatTooltip;
  footerClose: boolean = false;
  statusReopened = AppConstant.REOPENED;

  constructor(private route: Router, private service: UtilserviceService, public alertService: AlertService, private menu: MenuController, private translate: TranslateService, public dataService: DataService,
    private unviredCordovaSDK: UnviredCordovaSDK, private faIconLibrary:  FaIconLibrary,
    private _DomSanitizationService: DomSanitizer, public userPreferenceService: UserPreferenceService, public helpService: HelpService, public platform: Platform, private alertController: AlertController, public toastController: ToastController, public cameraService: CameraService, public navController: NavController,
    private emailComposer: EmailComposer, public modalController: ModalController, public iab: InAppBrowser, private screenOrientation: ScreenOrientation, public device: Device,
    private domSanitizer:DomSanitizer, private popoverController: PopoverController, public lmdService: LmdService,
    public platformService: PlatformService) {
    // const obj = this.service.getBaselineparam();
    // this.baselineArray = this.service.getBaselineArray();

this.faIconLibrary.addIcons(faHouse, faListCheck, faEnvelope, faBars, faGrip, faTimes, faCircleInfo, faPlus, faTrash,faPencil, faEye, faCheck);
    this.currentScreenOrientation = this.screenOrientation.type;

    this.unviredCordovaSDK.registerNotifListener().subscribe((result) => {
      this.unviredCordovaSDK.logInfo("HOME", "registerNotifListener", JSON.stringify(result));
      switch (result.type) {
        case NotificationListenerType.dataReceived:
          this.syncButtonClicked = false;
          break;
        case NotificationListenerType.dataChanged:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          this.refreshData();
          break;
        case NotificationListenerType.dataSend:
          this.syncButtonClicked = false;
          this.refreshData();
          break;
        case NotificationListenerType.appReset:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.attachmentDownloadSuccess:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.attachmentDownloadError:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          this.refreshData();
          break;
        case NotificationListenerType.incomingDataProcessingFinished:
          // this.syncButtonClicked = false;
          // this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.attachmentDownloadWaiting:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.infoMessage:
          this.dataService.clearDataRefresh();
          this.refreshData();
          this.handleInfoMessage(result);
          this.syncButtonClicked = false;
          break;
        case NotificationListenerType.serverError:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        case NotificationListenerType.attachmentDownloadCompleted:
          this.syncButtonClicked = false;
          this.dataService.clearDataRefresh();
          break;
        // default: 
        //   this.syncButtonClicked = false;
        //   this.dataService.clearDataRefresh();
        //   this.refreshData();
        //   break;
      }
    });
    this.getLoggedInUser();
    this.cameraService.reset()
    this.getSelectedInspectionHeader();
    this.productName = this.inspectionHeader.PRODUCT ? this.inspectionHeader.PRODUCT : '';
    this.certNumber = this.inspectionHeader.CERT_NAME;
    this.startPoint = this.inspectionHeader.START_POINT;
    this.productConfig = this.inspectionHeader.PRODUCT_CONFIG
    this.selectedUnit = this.dataService.selectedUom;
    this.inspectionName = this.inspectionHeader.INSPECTION_NAME;


    // const whereClause =  "INSPECTION_ID like '" + this.inspectionHeader.INSPECTION_ID  + "'";
    // this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_MEASUREMENT_ITEM, whereClause).then((result) => {
    //   if (result.type === ResultType.success) {
    //     this.baselineArray = result.data;
    //   } else {
    //     console.log('error' + JSON.stringify(result));
    //   }
    // }, error => {
    //   console.log('error' + JSON.stringify(error));
    // });

  }

  async getSelectedInspectionHeader() {
    this.inspectionHeader = this.service.getSelectedInspectionHeader()
    this.productName = this.inspectionHeader.PRODUCT ? this.inspectionHeader.PRODUCT : '';
    this.certNumber = this.inspectionHeader.CERT_NAME;
    this.startPoint = this.inspectionHeader.START_POINT;
    this.productConfig = this.inspectionHeader.PRODUCT_CONFIG
    this.inspectionName = this.inspectionHeader.INSPECTION_NAME;
    this.selectedUnit = this.dataService.selectedUom;
    var temp = await this.dataService.getSelectedHeaderFromDb(this.inspectionHeader.INSPECTION_ID)
    if (temp.type == ResultType.success) {
      if (temp.data.length > 0) {
        this.inspectionHeader = temp.data[0]
        this.productName = this.inspectionHeader.PRODUCT ? this.inspectionHeader.PRODUCT : '';
        this.certNumber = this.inspectionHeader.CERT_NAME;
        this.selectedUnit = this.dataService.selectedUom;
        this.startPoint = this.inspectionHeader.START_POINT;
        this.productConfig = this.inspectionHeader.PRODUCT_CONFIG
        this.inspectionName = this.inspectionHeader.INSPECTION_NAME;
      }
    }
  }
  test() { }

  async ionViewWillEnter() {
    this.labEmployeeObservationList = [
      // { specimen: 1, type: 'External Abrasion', start: '', end: '', notes: '', typeValue: 'external', DATA: '', photos:[]},
      // { specimen: 2, type: 'Internal Abrasion', start: '', end: '', notes: '', typeValue: 'internal', DATA: '', photos:[] },
      // { specimen: 3, type: 'Cut Strands', start: '', end: '', notes: '', typeValue: 'cuts', DATA: '', photos:[] },
      // { specimen: 4, type: 'Twist', start: '', end: '', notes: '', typeValue: 'twist', DATA: '', photos:[] },
      // { specimen: 5, type: 'Compression', start: '', end: '', notes: '', typeValue: 'compression', DATA: '', photos:[] },
      // { specimen: 6, type: 'Glazing', start: '', end: '', notes: '', typeValue: 'glazing', DATA: '', photos:[] },
      // { specimen: 7, type: 'Debris', start: '', end: '', notes: '', typeValue: 'debris', DATA: '', photos:[] },
      // { specimen: 8, type: 'Pulled Strands', start: '', end: '', notes: '', typeValue: 'pulled', DATA: '', photos:[] },
      // { specimen: 9, type: 'Discoloration', start: '', end: '', notes: '', typeValue: 'discoloration', DATA: '', photos:[] },
      // { specimen: 10, type: 'Inconsistent DIameter', start: '', end: '', notes: '', typeValue: 'inconsistent diameter', DATA: '', photos:[] }
    ];

    this.isLoggedInUserEmployee = this.dataService.selectedRole == 'Employee' ? true : false;
    // this.isLoggedInUserEmployee = true;
    this.getLoggedInUser()
    this.readFlag = this.service.getInspectionReadFlag();
    if (this.readFlag === 'readOnly') {
      this.dissableAdd = true;
      this.dataService.showEmail(true)
    } else {
      this.dissableAdd = false;
      this.dataService.showEmail(false)
      
    }
    // await this.alertService.present().then(() => {
      setTimeout(async () => {
        this.selectAllMeasurements().then(async () => {
          if (this.inspectionHeader.INSPECTION_STATUS == AppConstant.READY || this.inspectionHeader.INSPECTION_STATUS == AppConstant.COMPLETED || this.inspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED) {
            if ((this.inspectionHeader.INSPECTION_STATUS == AppConstant.READY && this.inspectionHeader.SYNC_STATUS == "3") || (this.inspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED && (this.inspectionHeader.SYNC_STATUS == "3" || this.inspectionHeader.SYNC_STATUS == "0"))) {
              this.dissableAdd = false
              // var temp = await this.updateInspectionHeader()
              // if (temp.type == ResultType.success) {
              //   this.service.setSelectedInspectionHeader(this.inspectionHeader)
              // }
              this.service.setSelectedInspectionHeader(this.inspectionHeader)
            } else {
              this.dissableAdd = true;
            }
          } else {
            this.dissableAdd = false
          }

          this.getCurrentExternal().then(() => {
          });
          this.getCurrentInternal().then(() => {
          });
          try {
            await this.alertService.dismiss();
          } catch(err) {
            console.log(err)
          }
        }, async error => {
          if (this.inspectionHeader.INSPECTION_STATUS == AppConstant.READY || this.inspectionHeader.INSPECTION_STATUS == AppConstant.COMPLETED || this.inspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED) {
            if ((this.inspectionHeader.INSPECTION_STATUS == AppConstant.READY && this.inspectionHeader.SYNC_STATUS == "3") || (this.inspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED && (this.inspectionHeader.SYNC_STATUS == "3" || this.inspectionHeader.SYNC_STATUS == "0"))) {
              this.dissableAdd = false
            } else {
              this.dissableAdd = true;
            }
            this.dissableAdd = true;
          }
          try {
            await this.alertService.dismiss();
          } catch(err) {
            console.log(err)
          }
        });
        await this.selectSpecimens();
      }, 500);
    // });
    // this.alertService.present().then(()=> {
    //   setTimeout(() => {
    if (this.showToastOnLoad == true) {
      this.presentReportToast();
    }
    await this.checkForPurchaceInformation();
  }

  async refreshData() {
    this.readFlag = this.service.getInspectionReadFlag();
    if (this.readFlag === 'readOnly') {
      this.dissableAdd = true;
    } else {
      this.dissableAdd = false;
    }
    await this.alertService.present().then(() => {
      setTimeout(async () => {
        this.selectAllMeasurements().then(async () => {
          if (this.inspectionHeader.INSPECTION_STATUS == AppConstant.READY || this.inspectionHeader.INSPECTION_STATUS == AppConstant.COMPLETED || this.inspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED) {
            if ((this.inspectionHeader.INSPECTION_STATUS == AppConstant.READY && this.inspectionHeader.SYNC_STATUS == "3") || (this.inspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED && (this.inspectionHeader.SYNC_STATUS == "3" || this.inspectionHeader.SYNC_STATUS == "0"))) {
              var temp = await this.updateInspectionHeader()
              if (temp.type == ResultType.success) {
                this.service.setSelectedInspectionHeader(this.inspectionHeader)
              }
              this.service.setSelectedInspectionHeader(this.inspectionHeader)
              this.dissableAdd = false
            } else {
              this.dissableAdd = true;
            }
          }

          this.getCurrentExternal().then(() => {
          });
          this.getCurrentInternal().then(() => {
          });
          try {
            await this.alertService.dismiss();
          } catch(err) {
            console.log(err)
          }
        }, async error => {
          if (this.inspectionHeader.INSPECTION_STATUS == AppConstant.READY || this.inspectionHeader.INSPECTION_STATUS == AppConstant.COMPLETED || this.inspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED) {
            if ((this.inspectionHeader.INSPECTION_STATUS == AppConstant.READY && this.inspectionHeader.SYNC_STATUS == "3") || (this.inspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED && (this.inspectionHeader.SYNC_STATUS == "3" || this.inspectionHeader.SYNC_STATUS == "0"))) {
              this.dissableAdd = false
            } else {
              this.dissableAdd = true;
            }
            this.dissableAdd = true;
          }
          try {
            await this.alertService.dismiss();
          } catch(err) {
            console.log(err)
          }
        });
        if(this.isLoggedInUserEmployee) {
          await this.selectSpecimens();
        }
        
      }, 500);
    });
    // this.alertService.present().then(()=> {
    //   setTimeout(() => {

  }

  async selectSpecimens() {
    this.selectAllLMDs().then((data)=>{
      this.labEmployeeSpecimenslist = [];
      data.forEach(ele=>{
        // Parse the stringified property
        const parsedObject = JSON.parse(ele.LMD_DATA);

        // Merge the original object and the parsed object
        const mergedLmdObject = {
            ...ele,
            ...parsedObject
        };

        // Remove the original stringified property if desired
        delete mergedLmdObject.LMD_DATA;
        this.labEmployeeSpecimenslist.push(mergedLmdObject);
        // if(this.labEmployeeSpecimenslist.length>0) {
        //   let index = this.labEmployeeSpecimenslist.findIndex(ele=>{ele.LMD_ID==mergedLmdObject.LMD_ID});
        //   if(index !=-1) {
        //     this.labEmployeeSpecimenslist.push(mergedLmdObject);
        //   }
        // } else {
        //   this.labEmployeeSpecimenslist.push(mergedLmdObject);
        // }
      })
    })
  }

  public handleInfoMessage(result) {

    this.unviredCordovaSDK.logInfo("Utility", "handleInfoMessage()", "Info Message:" + JSON.stringify(result, null, 2))

    if (!result.data || result.data.length == 0) {
      return
    }

    let messages: any = result.data
    if (!messages || messages.length == 0) {
      return
    }

    let messageTitle: string = messages[0].CATEGORY
    if (!messageTitle) {
      messageTitle = "Information"
    }

    var messageString = ''
    messages.forEach(element => {
      if (element.MESSAGE) {
        messageString += element.MESSAGE + '<br>'
      }
    });

    this.showAlert(messageTitle.substring(0, 1).toUpperCase() + messageTitle.substr(1).toLowerCase(), messageString)
  }

  async showAlert(title, message) {
    const alert = await this.alertController.create({
      header: title,
      message: message,
      buttons: ['OK']
    });
    await alert.present();
  }

  async updateInspectionHeader() {
    if(this.inspectionHeader.INSPECTION_STATUS!=AppConstant.REOPENED) {
      this.inspectionHeader.INSPECTION_STATUS = AppConstant.IN_PROGRESS;
    }
    return await this.unviredCordovaSDK.dbUpdate(AppConstant.TABLE_INSPECTION_HEADER, this.inspectionHeader, `INSPECTION_ID like '${this.inspectionHeader.INSPECTION_ID}'`)
  }

  ionViewDidLeave() {
    this.helpService.exitHelpMode();
    this.dataService.showEmail(false)
    this.unviredCordovaSDK.logInfo("OBSERVATION", "ionViewDidLeave", "leaving observation screen");
  }
  async getCurrentExternal() {
    if (this.inspectionHeader.INSPECTION_STATUS == AppConstant.READY || this.inspectionHeader.INSPECTION_STATUS == AppConstant.COMPLETED) {
      this.service.setObservationReadFlag('readOnly');
    } else {
      this.service.setObservationReadFlag('');
    }
    var temp;
    var result = await this.unviredCordovaSDK.dbExecuteStatement("SELECT * from MEASUREMENT WHERE INSPECTION_ID like '" + this.inspectionHeader.INSPECTION_ID + "' AND MEAS_TYPE_ID like 'External' ORDER BY MEAS_TIMESTAMP DESC LIMIT 1");
    if (result.type == ResultType.success) {
      if (result.data.length > 0) {
        temp = result.data;
        this.currentExternalObj = temp;
        this.obj = JSON.parse(temp[0].DATA)
        // this.obj = this.service.getcurrentExternal();
        // const obj1 = this.service.getcurrentInternal();
        if (this.obj == null) {
          this.external = 0;
        } else {
          this.external = this.obj.external;
          if (this.external == 0 && this.obj.externalDamageType && this.obj.externalDamageType != "") {
            this.external = this.obj.externalDamageType
          }
        }
      } else {
        this.external = 0;
      }
    }
  }
  async getCurrentInternal() {
    if (this.inspectionHeader.INSPECTION_STATUS == AppConstant.READY || this.inspectionHeader.INSPECTION_STATUS == AppConstant.COMPLETED) {
      this.service.setObservationReadFlag('readOnly');
    } else {
      this.service.setObservationReadFlag('');
    }
    var temp;
    var result = await this.unviredCordovaSDK.dbExecuteStatement("SELECT * from MEASUREMENT WHERE INSPECTION_ID like '" + this.inspectionHeader.INSPECTION_ID + "' AND MEAS_TYPE_ID like 'Internal' ORDER BY MEAS_TIMESTAMP DESC LIMIT 1");
    if (result.type == ResultType.success) {
      if (result.data.length > 0) {
        temp = result.data;
        this.currentInternalObj = temp;
        this.obj1 = JSON.parse(temp[0].DATA)
        // this.obj = this.service.getcurrentExternal();
        // const obj1 = this.service.getcurrentInternal();
        if (this.obj1 == null) {
          this.internal = 0;
        } else {
          this.internal = this.obj1.internal;
          if (this.internal == 0 && this.obj1.internalDamageType && this.obj1.internalDamageType != "") {
            this.internal = this.obj1.internalDamageType
          }
        }
      } else {
        this.internal = 0;
      }
    }
  }

  async selectAllLMDs() {
    var query = `Select *
                from LMD_HEADER LMD
                WHERE LMD_DATA LIKE '%"asset":"${this.inspectionHeader.ASSET_ID}"%'
                  AND LMD_DATA LIKE '%"accountNo":"${this.inspectionHeader.ACCOUNT_ID}"%'
                  AND LMD_DATA LIKE '%"certNum":"${this.inspectionHeader.CERTIFICATE_NUM}"%'
                  AND LMD_DATA LIKE '%"rps":"${this.inspectionHeader.RPS}"%'
                  AND LMD.LMD_TYPE = 'testing'
                  AND EXTFLD1 = '${this.inspectionHeader.EXTERNAL_ID}'`;
    // ! use external id to reference specimens related to the inspection if the inspection is reopened else use the inspection_id to fetch the specimens
    let specimenRes = await this.unviredCordovaSDK.dbExecuteStatement(query)
    if (specimenRes.type == ResultType.success) {
      return specimenRes.data;
    } else {
      console.log("error reading LMD_HEADER db");
    }
  }

  async selectAllMeasurements() {
    // let attRes = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_ATTACHMENT,``);
    // if(attRes.type==ResultType.success) {
    //   console.log(attRes.data);
    // }
    this.baselineArray = [];
    var temp;
    var result = await this.dataService.selectAllMeasurements(this.inspectionHeader)
    if (result.type == ResultType.success) {
      this.baselineArray = [];
      temp = result.data;
      // this.baselineArray = result.data;

      var resultData = result.data;
      for (var x = 0; x < resultData.length; x++) {
        resultData.sort(function (a, b) {
          var tempa = a;
          var tempb = b
          a = (moment(JSON.parse(a.DATA).CreatedDate).format('DD-MM-YYYY')).split('-').reverse().join('')
          b = (moment(JSON.parse(b.DATA).CreatedDate).format('DD-MM-YYYY')).split('-').reverse().join('')
          return a > b ? 1 : a < b ? -1 : a == b ? tempa.MEAS_TIMESTAMP > tempb.MEAS_TIMESTAMP ? 1 : tempa.MEAS_TIMESTAMP < tempb.MEAS_TIMESTAMP ? -1 : 0 : 0;
        });
      }
      resultData = resultData.reverse()

      for (var i = 0; i < resultData.length; i++) {
        this.service.setBaselineparam(JSON.parse(resultData[i].DATA))
        // temp[i].DATA = JSON.parse(this.baselineArray[i].DATA)

        let baseLineObject = {}
        baseLineObject["DATA"] = JSON.parse(resultData[i].DATA)
        if (baseLineObject["DATA"].otherData) {
          baseLineObject["ending"] = baseLineObject["DATA"].otherData.ending
        }
        else {
          baseLineObject["ending"] = '-'
        }


        this.baselineArray.push(baseLineObject)

        // this.baselineArray[i].DATA = JSON.parse(this.baselineArray[i].DATA)

        // this.baselineArray[i].ending = this.baselineArray[i].DATA.otherData.ending
      }
      console.log('Array data?////////////////////////', JSON.stringify(this.baselineArray));
      this.labEmployeeObservationList = [
        // { specimen: 0, type: '', start: '', end: '', notes: '', typeValue: '', DATA: '', photos:[]},
        // { specimen: 2, type: 'Internal Abrasion', start: '', end: '', notes: '', typeValue: 'internal', DATA: '', photos:[] },
        // { specimen: 3, type: 'Cut Strands', start: '', end: '', notes: '', typeValue: 'cuts', DATA: '', photos:[] },
        // { specimen: 4, type: 'Twist', start: '', end: '', notes: '', typeValue: 'twist', DATA: '', photos:[] },
        // { specimen: 5, type: 'Compression', start: '', end: '', notes: '', typeValue: 'compression', DATA: '', photos:[] },
        // { specimen: 6, type: 'Glazing', start: '', end: '', notes: '', typeValue: 'glazing', DATA: '', photos:[] },
        // { specimen: 7, type: 'Debris', start: '', end: '', notes: '', typeValue: 'debris', DATA: '', photos:[] },
        // { specimen: 8, type: 'Pulled Strands', start: '', end: '', notes: '', typeValue: 'pulled', DATA: '', photos:[] },
        // { specimen: 9, type: 'Discoloration', start: '', end: '', notes: '', typeValue: 'discoloration', DATA: '', photos:[] },
        // { specimen: 10, type: 'Inconsistent DIameter', start: '', end: '', notes: '', typeValue: 'inconsistent diameter', DATA: '', photos:[] }
      ];
      this.service.clearAllData();
      this.service.setAllData(this.baselineArray);
      this.baselineArray = this.baselineArray
      // if(this.baselineArray.length == 2 ){
      //   if(this.inspectionHeader.INSPECTION_STATUS == AppConstant.IN_PROGRESS || this.inspectionHeader.INSPECTION_STATUS == AppConstant.READY) {
      //     if((this.baselineArray[0].DATA.type == 'Internal' || this.baselineArray[0].DATA.type == 'External') && (this.baselineArray[1].DATA.type == 'Internal' || this.baselineArray[1].DATA.type == 'External')) {
      //       if((this.baselineArray[0].DATA.type == 'Internal') && (this.baselineArray[1].DATA.type == 'Internal')) {
      //         this.emptyListInprogress = false;
      //       } else if((this.baselineArray[0].DATA.type == 'External') && (this.baselineArray[1].DATA.type == 'External')) {
      //         this.emptyListInprogress = false;
      //       } else {
      //       this.emptyListInprogress = true;
      //       }
      //     } else {
      //       this.emptyListInprogress = false;
      //     }
      //   } else {
      //     if((this.baselineArray[0].DATA.type == 'Internal' || this.baselineArray[0].DATA.type == 'External') && (this.baselineArray[1].DATA.type == 'Internal' || this.baselineArray[1].DATA.type == 'External')) {
      //       this.emptyListCompleted = true;
      //     } else {
      //       this.emptyListCompleted = false;
      //     }
      //   }
      // }  else
      //FOR EMPLOYEE LOGIN
      if (this.isLoggedInUserEmployee) {
        this.labEmployeeObservationList.forEach(el => {
          el.DATA = '';
          el.photos = [];
        })
        this.baselineArray.forEach(async config => {
          var type = ''
          var typeValue = ''
          // 
          let index;
          switch (config.DATA.type) {
            case 'External':
              index = this.labEmployeeObservationList.findIndex(ele => ele.typeValue == 'external')
              if(config.DATA.other === '' || config.DATA.other == null || config.DATA.other == undefined) {
                config.DATA.other = config.DATA.external
              } 
              type = 'External Abrasion'
              typeValue = 'external'
              break;

            case 'Internal':
              index = this.labEmployeeObservationList.findIndex(ele => ele.typeValue == 'internal')
              if(config.DATA.other === '' || config.DATA.other == null || config.DATA.other == undefined) {
                config.DATA.other = config.DATA.internal
              } 
              type = 'Internal Abrasion'
              typeValue = 'internal'
              break;

            case 'Cuts':
              index = this.labEmployeeObservationList.findIndex(ele => ele.typeValue == 'cuts')
              if(config.DATA.other === '' || config.DATA.other == null || config.DATA.other == undefined) {
                config.DATA.other = config.DATA.otherData.totalAreaLoss + ' %'
              } 
              type = 'Cut Strands'
              typeValue = 'cuts'
              break;

            case 'Twist':
              index = this.labEmployeeObservationList.findIndex(ele => ele.typeValue == 'twist')
              type = 'Twist'
              typeValue = 'twist'
              break;

            case 'Compression':
              index = this.labEmployeeObservationList.findIndex(ele => ele.typeValue == 'compression')
              type = 'Compression'
              typeValue = 'compression'
              break;

            case 'Melting':
              index = this.labEmployeeObservationList.findIndex(ele => ele.typeValue == 'melting')
              if(config.DATA.other === '' || config.DATA.other == null || config.DATA.other == undefined) {
                config.DATA.other = config.DATA.otherData.totalAreaLoss + ' %'
              } 
              type = 'Melting'
              typeValue = 'melting'
              break;

            case 'Contamination':
              index = this.labEmployeeObservationList.findIndex(ele => ele.typeValue == 'contamination')
              type = 'Contamination'
              typeValue = 'contamination'
              break;

            case 'Pulled':
              index = this.labEmployeeObservationList.findIndex(ele => ele.typeValue == 'pulled')
              if(config.DATA.other === '' || config.DATA.other == null || config.DATA.other == undefined) {
                config.DATA.other = config.DATA.otherData.totalAreaLoss + ' %'
              } 
              type = 'Pulled Strands'
              typeValue = 'pulled'
              break;

            case 'Discoloration':
              index = this.labEmployeeObservationList.findIndex(ele => ele.typeValue == 'discoloration')
              type = 'Discoloration'
              typeValue = 'discoloration'
              break;

            case 'Diameter':
            case 'Inconsistent Diameter':
              index = this.labEmployeeObservationList.findIndex(ele => ele.typeValue == 'inconsistent diameter')
              type = 'Inconsistent Diameter'
              typeValue = 'inconsistent diameter'
              break;

            case 'Linear Damage':
              index = this.labEmployeeObservationList.findIndex(ele => ele.typeValue == 'linear damage')
              type = 'Linear Damage'
              typeValue = 'linear damage'
              break;

            case 'Kinking':
              index = this.labEmployeeObservationList.findIndex(ele => ele.typeValue == 'kinked yarns')
              type = 'Kinked Yarns'
              typeValue = 'kinked yarns'
              break;

            case 'Parting':
              index = this.labEmployeeObservationList.findIndex(ele => ele.typeValue == 'parting')
              type = 'Parting'
              typeValue = 'parting'
              break;
          }
          // if (this.labEmployeeObservationList[index].DATA == undefined || this.labEmployeeObservationList[index].DATA == '')  {
          //   this.labEmployeeObservationList[index]['DATA'] = config.DATA;
          // } else {
            var new_obj = { specimen: 0, type: '', start: '', end: '', notes: '', typeValue: '', DATA: '', photos:[]}
            new_obj.specimen = this.labEmployeeObservationList.length + 1
            new_obj.DATA = config.DATA;
            new_obj.type = type;
            new_obj.typeValue = typeValue;
            // for(let m=0;m<config.DATA.externalImage.length;m++) {
            //   let image = JSON.parse(JSON.stringify(config.DATA.externalImage[m]))
            //   let url:SafeUrl = await this.cameraService.getNativeURL(image.Image.changingThisBreaksApplicationSecurity);
            //   new_obj.photos.push(url);
            // };
            this.labEmployeeObservationList.push(new_obj);
            // for(let m=0;m<config.DATA.externalImage.length;m++) {
            //     let image = JSON.parse(JSON.stringify(config.DATA.externalImage[m]))
            //     let url = await this.cameraService.getNativeURL(image.Image.changingThisBreaksApplicationSecurity);
            //     this.labEmployeeObservationList[index].photos.push(url);
            // };
        });
        this.labEmployeeObservationList = this.labEmployeeObservationList.sort((a, b) => {
          if (a.DATA.start == null || a.DATA.start === '' || a.DATA.start == undefined) return 1;
          if (b.DATA.start == null || b.DATA.start === '' || b.DATA.start == undefined) return -1;
          if (a.DATA.start < b.DATA.start) return -1;
          if (a.DATA.start > b.DATA.start) return 1;
          if (a.DATA.start == b.DATA.start) {
            if(a.DATA.otherData.ending < b.DATA.otherData.ending) return -1;
            if(a.DATA.otherData.ending > b.DATA.otherData.ending) return 1;
          }
          return 0;
        });
        this.labEmployeeObservationList.forEach(async el => {
          for(let m=0;m<el.DATA.externalImage.length;m++) {
            if( this.device.platform != 'browser') {
              let image = JSON.parse(JSON.stringify(el.DATA.externalImage[m]))
              let url =  this._DomSanitizationService.bypassSecurityTrustUrl(image.Image.changingThisBreaksApplicationSecurity);
              el.photos.push(url);
            } else {
              el.photos.push(el.DATA.externalImage[m].Image);
            }
          };
        })
        // console.log(this.labEmployeeObservationList)
      }

      if (this.baselineArray.length == 0) {
        if (this.inspectionHeader.INSPECTION_STATUS == AppConstant.IN_PROGRESS || this.inspectionHeader.INSPECTION_STATUS == AppConstant.READY ) {
          this.emptyListInprogress = true;
        } else {
          this.emptyListCompleted = true;
        }
      } else {
        this.emptyListInprogress = false;
        this.emptyListCompleted = false;
      }
    }
  }

  async ngOnInit() {
    // ! checking whether the selected Industry is Utility or not to hide the internal abrasion from the add observation dropdown list for Employee login
    this.selectedIndustry = await this.userPreferenceService.getUserPreference("industry")
    this.selectedIndustry = this.selectedIndustry != '' ? JSON.parse(this.selectedIndustry) : '';
    this.isUtility = (this.selectedIndustry.ID.includes('Utility'));
    this.showToastOnLoad = true;
    if (this.inspectionHeader.ADV_SUPRT_STATUS == AppConstant.REQUESTED) {
      this.disableAdvancedPayment = false;
    } else {
      this.disableAdvancedPayment = true;
    }
    this.readOnly = (this.inspectionHeader.INSPECTION_STATUS == AppConstant.COMPLETED || this.inspectionHeader.INSPECTION_STATUS=='')
    window.addEventListener('keyboardDidHide', () => {
      setTimeout(()=>{
        this.footerClose = false;
      },10)
      
    });
    window.addEventListener('keyboardDidShow', (event) => {
      setTimeout(()=>{
        this.footerClose = true;
      },10)
    });
  }

  async newInspection() {
    this.tooltip.hide();
    this.service.setMeasurementEditMode(false);
    this.cameraService.setImages([], {})
    setTimeout(() => {
      this.route.navigate(['new-observation']);
    });
  }

  async currentExternal() {
    const obj = this.obj;
    if (obj == null) {
    } else {
      this.readFlag = this.service.getInspectionReadFlag();
      if (this.readFlag === 'readOnly') {
        this.service.setObservationReadFlag(this.readFlag);
      }
      this.service.setMeasurementEditMode(true);
      this.service.setExternalData(this.obj);
      switch (this.obj.type) {
        case 'external':
          await this.cameraService.setImages(obj.externalImage, obj.originalImages)
          this.route.navigateByUrl('/external-abrasion');
          break;
      }
    }
  }
  async currentInternal() {
    const obj = this.obj1;
    if (obj == null) {
    } else {
      this.readFlag = this.service.getInspectionReadFlag();
      if (this.readFlag === 'readOnly') {
        this.service.setObservationReadFlag(this.readFlag);
      }
      this.service.setMeasurementEditMode(true);
      this.service.setExternalData(this.obj1);
      switch (obj.type) {
        case 'Internal':
          await this.cameraService.setImages(obj.externalImage, obj.originalImages)
          this.route.navigateByUrl('/internal-abrasion');
          break;
      }
    }
  }
  async review(item) {
    this.readFlag = this.service.getInspectionReadFlag();
    if (this.readFlag === 'readOnly') {
      this.service.setObservationReadFlag(this.readFlag);
    }

    this.service.setMeasurementEditMode(true);
    // this.event.publish('reviewData', item);
    if (this.platformId === 'electron') {
      let itemCopy = JSON.parse(JSON.stringify(item));
      this.service.setExternalData(itemCopy);
    } else {
      this.service.setExternalData(item);
    }

    console.log(" ++++++++++++" + item.externalImage)
    await this.cameraService.setImages(item.externalImage, item.originalImages)

    switch (item.type) {

      case 'External':
        this.route.navigateByUrl('/external-abrasion');
        break;
      case 'Internal':
        this.route.navigateByUrl('/internal-abrasion');
        break;
      case 'Cuts':
        this.route.navigateByUrl('/cuts');
        break;
      case 'Discoloration':
        this.route.navigateByUrl('/discoloration');
        break;
      case 'Melting':
        this.route.navigateByUrl('/melting');
        break;
      case 'Caging':
        this.route.navigateByUrl('/caging');
        break;
      case 'Contamination':
        this.route.navigateByUrl('/contamination');
        break;
      case 'Parting':
        this.route.navigateByUrl('/parting');
        break;
      case 'Twist':
        this.route.navigateByUrl('/twist');
        break;
      case 'Diameter':
      case 'Inconsistent Diameter':
        this.route.navigateByUrl('/inconsistent-diameter');
        break;
      case 'Compression':
        this.route.navigateByUrl('/compression');
        break;
      case 'Wire Breaks':
        this.route.navigateByUrl('/wire-breaks');
        break;
      case 'Corrosion':
        this.route.navigateByUrl('/corrosion');
        break;
      case 'Kinking':
        this.route.navigateByUrl('/kinking');
        break;
      case 'Basket Deformation':
        this.route.navigateByUrl('/basket-deformation');
        break;
      case 'Protrusion':
        this.route.navigateByUrl('/protrusion');
        break;
      case 'Heat Damage':
        this.route.navigateByUrl('/heat-damage');
        break;
      case 'Waviness':
        this.route.navigateByUrl('/waviness');
        break;
      case 'Flattening':
        this.route.navigateByUrl('/flattening');
        break;
      case 'Pulled':
        this.route.navigateByUrl('/pulled');
        break;
      case 'Linear Damage':
        this.route.navigateByUrl('/linear-damage');
        break;
    }
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  async resetConfirm(item) {
    const alert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('Delete Measurement'),
      message: '<strong>' + this.translate.instant('You want to delete this measurement') + '</strong>',
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
          }
        }, {
          text: this.translate.instant('Delete'),
          handler: () => {
            this.deleteMeasurement(item);
            // const id = this.service.getBaselineArray().findIndex(o => o.id === item.id);
            // this.service.getBaselineArray().splice(id, 1);
            // const obj = this.service.getcurrentExternal();
            // if (obj == null) {
            //   this.external = 0;
            // }
          }
        }
      ]
    });
    await alert.present();
  }

  async deleteMeasurement(item) {
    this.alertService.present()
    var result = await this.deleteMeasurementFromDb(item)
    if (result.type == ResultType.success) {
      this.refreshData()
      this.alertService.dismiss();
    } else {
      this.refreshData()
      this.alertController.dismiss();
      this.unviredCordovaSDK.logError("observations", " deleteMeasurements", JSON.stringify(result));
    }
  }

  async deleteMeasurementFromDb(item) {
    console.log
    if(this.inspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED) {
      let measRes = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_MEASUREMENT_ITEM,`INSPECTION_ID='${this.inspectionHeader.INSPECTION_ID}' AND DATA LIKE '%"id":"${item.DATA.id}"%'`);
      if(measRes.type==ResultType.success) {
        if(measRes.data.length>0) {
          let measItem = measRes.data[0];
          if(measItem!=undefined) {
            await this.unviredCordovaSDK.dbDelete("INSPECTION_ATTACHMENT", "TAG1 like '" + measItem.MEAS_ID + "'")
            await this.unviredCordovaSDK.dbDelete("ANNOTATION", "MEAS_ID like '" + measItem.MEAS_ID + "'")
            await this.unviredCordovaSDK.dbDelete("MEASUREMENT", "MEAS_ID like '" + measItem.MEAS_ID + "'")
          }
        }
      } else {
        console.log("error reading measurement table:",measRes.error);
      }
    }

    await this.unviredCordovaSDK.dbDelete("INSPECTION_ATTACHMENT", "TAG1 like '" + item.DATA.id + "'")
    await this.unviredCordovaSDK.dbDelete("ANNOTATION", "MEAS_ID like '" + item.DATA.id + "'")
    return await this.unviredCordovaSDK.dbDelete("MEASUREMENT", "MEAS_ID like '" + item.DATA.id + "'")
  }

  async completeInspection() {
    this.unviredCordovaSDK.logInfo("OBSERVATION", "completeInspection", "completeInspection called");
    var temp = await this.completeObservations();
  }

  async completeObservations() {
    this.unviredCordovaSDK.logInfo("OBSERVATION", "completeObservations", "completeObservations called");
    // Auto fill last valeus to inspection length
    // this.obj.end = this.inspectionHeader.INSPECTED_LENGTH
    // this.obj1.end = this.inspectionHeader.INSPECTED_LENGTH
    // this.currentExternalObj[0].DATA = JSON.stringify(this.obj)
    // this.currentInternalObj[0].DATA = JSON.stringify(this.obj1)
    // var tempInternam = await this.unviredCordovaSDK.dbUpdate("MEASUREMENT", this.currentExternalObj[0], "MEAS_ID like '" + this.currentExternalObj[0].MEAS_ID + "'")
    // var tempExternal = await this.unviredCordovaSDK.dbUpdate("MEASUREMENT", this.currentInternalObj[0], "MEAS_ID like '" + this.currentInternalObj[0].MEAS_ID + "'")
    await this.completeInspectionHeader();
    if(this.isLoggedInUserEmployee) {
      if(this.inspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED) {
        await this.completeExistingConfigurations();
        await this.completeExistingObservations();
        // await this.submitInspExistingAttachments();
      }
      await this.completeSpecimens();
    }
  }

  async submitInspExistingAttachments(item:any) { //~ this method will change the Object_Status of both the configurations and observations attachments for already submitted,during re-open inspection
    let attachRes = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_ATTACHMENT,`FID ='${this.inspectionHeader.LID}' AND TAG1 = '${item.MEAS_ID!=undefined ? item.MEAS_ID : item.CONFIG_ID}'`);
    if(attachRes.type==ResultType.success) {
      if(attachRes.data.length>0) {
        // let m=0;
        for(let m=0;m<attachRes.data.length;m++) {
          let attachItem = attachRes.data[m];
          if(attachItem.OBJECT_STATUS==0) {
            attachItem.OBJECT_STATUS=2;
            let TAG1 = item.MEAS_ID!=undefined ? item.MEAS_ID : item.CONFIG_ID;
            attachItem.TAG1 = TAG1+"~~OLD";
            let measRes = await this.unviredCordovaSDK.dbUpdate(AppConstant.TABLE_INSPECTION_ATTACHMENT,attachItem,`FID = '${this.inspectionHeader.LID}' AND LID = '${attachItem.LID}'`)
            if(measRes.type==ResultType.success) {
              console.log('%c successfully updated attachments item:',+attachItem, 'background: #0f21e6; color: #ffffff');
              // console.log("successfully updated attachments item:",attachItem);
            } else {
              console.log("error updating attachments item");
            }
          }
          // m++;
        }
      }
    } else {
      console.log("error reading configuration table:",attachRes.error)
    }

    let annotRes = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_ANNOTATION,`INSPECTION_ID = '${this.inspectionHeader.EXTERNAL_ID}' AND FID='${this.inspectionHeader.LID}'`);
    if(annotRes.type==ResultType.success) {
      if(annotRes.data.length>0) {
        // let m=0;
        for(let m=0;m<annotRes.data.length;m++) {
          let annotItem = annotRes.data[m];
          if(annotItem.OBJECT_STATUS==0) {
            annotItem.OBJECT_STATUS=2;
            annotItem.EXTFLD1 = "~~OLD";
            // await this.submitInspExistingAttachments(annotItem);
            let measRes = await this.unviredCordovaSDK.dbUpdate(AppConstant.TABLE_ANNOTATION,annotItem,`FID = '${this.inspectionHeader.LID}' AND LID = '${annotItem.LID}'`)
            if(measRes.type==ResultType.success) {
              console.log("successfully updated annotations item");
              await this.dataService.saveMeasurements(annotItem.DATA,true)
            } else {
              console.log("error updating annotations item");
            }
          }
          // m++;
        }
      }
    } else {
      console.log("error reading annotation table:",annotRes.error);
    }
  }

  async completeExistingConfigurations() {
    let result = await this.dataService.selectAllConfigMeasurements(this.inspectionHeader);
    if(result.type== ResultType.success) {
      if(result.data.length>0) {
        let m=0;
        while(m<result.data.length) {
          let configItem = result.data[m];
          if(configItem.OBJECT_STATUS==0) {
            configItem.OBJECT_STATUS=2;
          }
          await this.submitInspExistingAttachments(configItem);
            let measRes = await this.unviredCordovaSDK.dbUpdate(AppConstant.TABLE_CONFIGURATION_ITEM,configItem,`CONFIG_ID = '${configItem.CONFIG_ID}'`)
            if(measRes.type==ResultType.success) {
              console.log("successfully updated configuration item");
              // await this.dataService.saveMeasurements(configItem.DATA,true)
            } else {
              console.log("error updating configuration item");
            }
          m++;
        }
      }
    }
  }

  async completeExistingObservations() {
    let result = await this.dataService.selectAllMeasurements(this.inspectionHeader)
    if(result.type== ResultType.success) {
      if(result.data.length>0) {
        let m=0;
        while(m<result.data.length) {
          let measItem = result.data[m];
          if(measItem.OBJECT_STATUS==0) {
            measItem.OBJECT_STATUS=2;
          }
          await this.submitInspExistingAttachments(measItem);
          let measRes = await this.unviredCordovaSDK.dbUpdate(AppConstant.TABLE_MEASUREMENT_ITEM,measItem,`MEAS_ID = '${measItem.MEAS_ID}'`);
            if(measRes.type==ResultType.success) {
              console.log("successfully updated measurement item");
              // await this.dataService.saveMeasurements(measItem.DATA,true)
            } else {
              console.log("error updating measurement item");
            }
          m++;
        }
      }
    }
  }

  async completeSpecimens() {
    this.selectAllLMDs().then(specimens=>{
      specimens.forEach(async(spec)=>{
        // if(spec.LMD_STATUS != AppConstant.COMPLETED) {
          // spec.LMD_STATUS = AppConstant.READY;
          if(this.inspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED && spec.OBJECT_STATUS!=1 && spec.OBJECT_STATUS!=2) {
            spec.OBJECT_STATUS = 2;
            let specmnRes = await this.unviredCordovaSDK.dbUpdate(AppConstant.TABLE_LMD_HEADER, spec, `LMD_ID= '${spec.LMD_ID}'`);
            if(specmnRes.type == ResultType.success) {
            } else {
              console.log("error updating LMD_HEADER")
            }
          }
          
        // }
      })
      this.selectAllLMDs().then((specimenData:any)=>{
        console.log("selecting specimens");
        specimenData.forEach(async ele=>{
          console.log("submitting specimens");
          await this.submitLMD(ele);
        })
      })
    })
  }

  async completeInspectionHeader() {
    if(this.inspectionHeader.INSPECTION_STATUS!=AppConstant.REOPENED) {
      this.inspectionHeader.INSPECTION_STATUS = AppConstant.READY
    }
    this.inspectionHeader.ADV_SUPRT_STATUS = AppConstant.NOT_REQUESTED
    if (this.disableAdvancedPayment == false) {
      this.inspectionHeader.ADV_SUPRT_STATUS = AppConstant.REQUESTED
      // this.inspectionHeader.PAYMENT_STATUS = AppConstant.STARTED
    }
    let status = this.inspectionHeader.INSPECTION_STATUS== AppConstant.REOPENED ? AppConstant.REOPENED : AppConstant.READY;
    var result = await this.dataService.completeInspection(this.inspectionHeader,status);
    this.unviredCordovaSDK.logInfo("OBSERVATION", "completeInspectionHeader", "Item Updated" + result);
    if (result.type == ResultType.success) {
      setTimeout(() => {
        this.unviredCordovaSDK.logInfo("OBSERVATION", "completeInspectionHeader", "calling syncInspection" + this.inspectionHeader);
        this.syncInspection(this.inspectionHeader)
      }, 200);
    } else {
      this.unviredCordovaSDK.logError("observation", "completed inspection", "Error while updating item error: " + JSON.stringify(result))
    }
  }

  async syncInspection(item) {
    this.unviredCordovaSDK.logInfo("OBSERVATION", "syncInspection", "called syncInspection" + item);
    if (!this.syncButtonClicked) {
      this.syncButtonClicked = true;
      var temp = await this.dataService.getSelectedHeaderFromDb(item.INSPECTION_ID)
      this.unviredCordovaSDK.logInfo("OBSERVATION", "syncInspection", "called syncInspection" + temp);
      if (temp.type == ResultType.success) {
        if (temp.data.length > 0) {
          this.unviredCordovaSDK.logInfo("OBSERVATION", "syncInspection", "calling submitInspection" + temp);
          var result = await this.submitInspection(temp.data[0])
          if (result.type == ResultType.success) {
            if (this.device.platform == "browser") {
              this.deleteHeader(this.inspectionHeader)
            } else {
              this.dataService.refreshData();
              this.unviredCordovaSDK.logInfo("OBSERVATION", "syncInspection", "success" + result);
            }
          }
          this.alertService.dismiss()
        }
        console.log(JSON.stringify(temp))
        this.unviredCordovaSDK.logInfo("OBSERVATION", "syncInspection", "success temp value" + temp);
      } else {
        console.log(JSON.stringify(temp))
        this.unviredCordovaSDK.logInfo("OBSERVATION", "syncInspection", "error temp value" + temp);
      }

      if (this.disableAdvancedPayment == false) {
        this.showAdvancedAssistenceAlert();
      } else {

        if (this.device.platform == "browser") {
          // this.deleteHeader(this.inspectionHeader)
        } else {
          this.dataService.refreshData();
        }
        if (this.device.platform == "browser") {
          this.unviredCordovaSDK.dbSaveWebData();
        }
        this.presentToast()
        this.service.setInspState('open');
        this.navController.navigateBack(['inspection'])
      }

      // this.showAdvancedAssistenceAlert();  
    }
  }

  async submitLMD(item) {
    // await this.alertService.present();
    let inputObject = {
      "LMD_HEADER": item
    }
    // console.log("LID" + item.LID)
    // let sendLmdToServer: any = {};
    // let categoryLmd: any = {};
    if (this.device.platform == "browser") { //! browser is not neccessary since specimen in observation screen is only for employee login and browser doesn't support employee login
      this.unviredCordovaSDK.dbSaveWebData();
      // categoryLmd['LMD_HEADER'] = item;
      // sendLmdToServer['LMD'] = [categoryLmd];
      // return this.unviredSDK.syncForeground(RequestType.RQST, "", sendLmdToServer, AppConstant.PA_ROPE_INSPECTIONS_PA_CREATE_LMD, true)
      return this.unviredCordovaSDK.syncForeground(RequestType.RQST, inputObject, '',  AppConstant.PA_ROPE_INSPECTIONS_PA_CREATE_LMD, true)   
    } else {
      // if(this.alertService.isLoading) {
      //   await this.alertService.dismiss();
      // }
      return this.unviredCordovaSDK.syncBackground(RequestType.RQST, inputObject, "", AppConstant.PA_ROPE_INSPECTIONS_PA_CREATE_LMD, "LMD", item.LID, false)
    }
  }

  async submitInspection(item) {
    this.unviredCordovaSDK.logInfo("OBSERVATION", "submitInspection", "submitting item" + JSON.stringify(item));
    await this.alertService.present();
    let inputObject = {
      "INSPECTION_HEADER": item
    }
    console.log("LID" + item.LID)
    let sendInspectionToServer: any = {};
    let categoryInspection: any = {};
    if (this.device.platform == "browser") {
      categoryInspection['LMD_HEADER'] = item;
      sendInspectionToServer['LMD'] = [categoryInspection];
      return this.unviredCordovaSDK.syncForeground(RequestType.RQST, inputObject, '', AppConstant.PA_ROPE_INSPECTIONS_PA_CREATE_INSPECTION, true)
    } else {
      this.unviredCordovaSDK.logInfo("OBSERVATION", "submitInspection", "submitting Input Object:" + JSON.stringify(inputObject));
      if(item.INSPECTION_STATUS==AppConstant.REOPENED) {
        return this.unviredCordovaSDK.syncBackground(RequestType.RQST, inputObject, "", AppConstant.PA_ROPE_INSPECTIONS_PA_UPDATE_INSPECTION, "INSPECTION", item.LID, false)
      } else {
        return this.unviredCordovaSDK.syncBackground(RequestType.RQST, inputObject, "", AppConstant.PA_ROPE_INSPECTIONS_PA_CREATE_INSPECTION, "INSPECTION", item.LID, false)
      }
    }

    // this.labEmployeeSpecimenslist
  }

  async presentToast() {
    const toast = await this.toastController.create({
      message: this.translate.instant("Submitted Inspection"),
      duration: 2000,
      color: "dark",
      position: "middle"
    });
    toast.present();
  }

  async presentEmailToast() {
    const toast = await this.toastController.create({
      message: this.translate.instant("Email Request Submitted"),
      duration: 2000,
      color: "dark",
      position: "middle"
    });
    toast.present();
  }

  async presentAlertConfirm() {
    const alert = await this.alertController.create({
      message: '<strong>Complete inspection?</strong>',
      buttons: [
        {
          text: this.translate.instant('No'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {

          }
        }, {
          text: this.translate.instant('Yes'),
          handler: async () => {
            await this.completeInspection();
          }
        }
      ]
    });

    await alert.present();
  }

  backButtonClick() {
    if (this.device.platform == 'browser') {
      if (!this.dissableAdd) {
        this.showDataSaveAlert("backButton")
      } else {
        this.navController.pop();
      }
    } else {
      if (this.service.getFromfromInspection()) {
        setTimeout(() => {
          this.navController.navigateBack('/inspection')
        }, 1000);
      } else {
        setTimeout(() => {
          this.navController.navigateBack('/inspection-home')
        }, 1000);
      }
    }
  }

  async presentEmailAlertConfirm(email?: string) {
    var mail = await this.userPreferenceService.getUserPreference('email');
    const alert = await this.alertController.create({
      message: 'Please enter email id to send the inspection.',
      inputs: [
        {
          name: 'email',
          type: 'email',
          value: mail ? mail : '',
          placeholder: 'Email'
        }
      ],
      buttons: [
        {
          text: this.translate.instant('Cancel'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {

          }
        }, {
          text: this.translate.instant('Complete'),
          role: 'complete',
          handler: () => {
            console.log('Confirm Ok');
          }
        }
      ]
    });

    await alert.present();
    await alert.onDidDismiss().then(async (data) => {
      if (data.role == 'complete') {
        if (data.data.values.email != '') {
          if (email == "email") {
            this.emailInspection(data.data.values.email);
          }
        } else {
          this.presentEmailAlertConfirm(email);
          setTimeout(() => {
            this.showAlert("", "Please enter id email to complete")
          }, 500);
          return false;
        }
      } else {

      }
    })
  }

  async emailInspection(emailId) {
    var input = new INPUT_SEND_REPORT_EMAIL_HEADER()
    input.INSP_NAME = this.inspectionName
    input.EMAIL_ID = emailId
    let inputObject = {
      "INPUT_SEND_REPORT_EMAIL_HEADER": input
    }
    var result;
    let sendEmailInputToServer: any = {};
    let categoryEmailInput: any = {};
    if (this.device.platform == "browser") {
      this.alertService.preasentLoading();
      categoryEmailInput['INPUT_SEND_REPORT_EMAIL_HEADER'] = input;
      sendEmailInputToServer['INPUT_SEND_REPORT_EMAIL'] = [categoryEmailInput];
      result = await this.unviredCordovaSDK.syncForeground(RequestType.RQST, "", sendEmailInputToServer, AppConstant.PA_ROPE_INSPECTIONS_PA_SEND_REPORT_EMAIL, true)
    } else {
      result = await this.unviredCordovaSDK.syncBackground(RequestType.QUERY, inputObject, "", AppConstant.PA_ROPE_INSPECTIONS_PA_SEND_REPORT_EMAIL, "INPUT_SEND_REPORT_EMAIL_HEADER", '', false)
    }
    if (result.type == ResultType.success) {
      this.presentEmailToast()
    } else {
      this.unviredCordovaSDK.logInfo("Observation", "email inspection", "Email")
    }

  }

  async emailInspectionOld(emailId) {

    // this.emailComposer.isAvailable().then((available: boolean) =>{
    //   if(available) {
    var message = "Samson Inspection <br>";
    message = message + "Rope ID : " + this.inspectionHeader.USER_UNIQUE_ID + '<br>'
    message = message + "Product : " + this.inspectionHeader.PRODUCT + '<br>'
    if (this.inspectionHeader.Jacketed == 1) {
      message = message + "Jacketed : Yes<br>"
    } else {
      message = message + "Jacketed : No<br>"
    }
    message = message + "Construction : " + this.inspectionHeader.CONSTRUCTION + '<br>'
    message = message + "Color : " + this.inspectionHeader.COLOR + '<br>'
    message = message + "Configuration : " + this.inspectionHeader.PRODUCT_CONFIG + '<br>'
    if (this.inspectionHeader.CHAFE_TYPE != '') {
      message = message + "Chafe : " + this.inspectionHeader.CHAFE_TYPE + '<br>'
    }
    message = message + "Diameter : " + this.inspectionHeader.DIAM + " " + this.inspectionHeader.DIAM_UOM + "<br>"
    message = message + "Length : " + this.inspectionHeader.CURRENT_LENGTH + " " + this.inspectionHeader.LENGTH_UOM + "<br><br><hr /><br>"

    for (var i = 0; i < this.baselineArray.length; i++) {
      if (this.baselineArray[i].DATA.type == "External" || this.baselineArray[i].DATA.type == "Internal") {
        message = message + this.baselineArray[i].DATA.type + " Abrasion <br>"
      } else {
        message = message + this.baselineArray[i].DATA.type + " Observation <br>"
      }
      message = message + " Location : " + this.baselineArray[i].DATA.start + '<br>'
      message = message + " Layer : " + this.baselineArray[i].DATA.otherData.layerOptions + '<br>'
      if (this.baselineArray[i].DATA.type == "External" || this.baselineArray[i].DATA.type == "Internal") {
        message = message + " Notes : " + this.baselineArray[i].DATA.other + '<br>'
      } else {
        message = message + " Notes : " + this.baselineArray[i].DATA.otherData.measurementLength + '<br>'
      }
      if (this.baselineArray[i].DATA.type == "External") {
        message = message + " Severity : " + this.baselineArray[i].DATA.externalDamageType + '<br>'
      } else if (this.baselineArray[i].DATA.type == "Internal") {
        message = message + " Severity : " + this.baselineArray[i].DATA.internalDamageType + '<br>'
      } else {
        message = message + " Severity : " + this.baselineArray[i].DATA.level + '<br>'
      }
      message = message + " Photos : "
      if (this.baselineArray[i].DATA.externalImage != undefined && this.baselineArray[i].DATA.externalImage != "") {
        for (var index = 0; index < this.baselineArray[i].DATA.externalImage.length; index++) {
          var imageData = this.baselineArray[i].DATA.externalImage[index].changingThisBreaksApplicationSecurity
          imageData = imageData.substr(imageData.indexOf('_app_file_') + 10, imageData.length - 1)
          var fileName = imageData.substring(imageData.lastIndexOf('/') + 1)
          message = message + fileName + ", "

        }
      }

      message = message.substr(0, message.length - 2) + '<br><hr /><br>'


    }
    var today = new Date().getFullYear() + '-' + ("0" + (new Date().getMonth() + 1)).slice(-2) + '-' + ("0" + new Date().getDate()).slice(-2)
    var attachments = [];
    var result = await this.unviredCordovaSDK.getAttachmentFolderPath()
    console.log(JSON.stringify(result))
    if (result.type == ResultType.success) {
      for (var a = 0; a < this.baselineArray.length; a++) {
        if (this.baselineArray[a].DATA.externalImage != undefined && this.baselineArray[a].DATA.externalImage != "") {
          var attachmentLength = this.baselineArray[a].DATA.externalImage.length
          for (var index = 0; index < this.baselineArray[a].DATA.externalImage.length; index++) {
            var imageData = this.baselineArray[a].DATA.externalImage[index].changingThisBreaksApplicationSecurity
            imageData = imageData.substr(imageData.indexOf('_app_file_') + 10, imageData.length - 1)
            var fileName = imageData.substring(imageData.lastIndexOf('/') + 1)
            imageData = "file://" + result.data.substr(0, result.data.lastIndexOf('Documents')) + "/Documents/" + fileName;
            if (this.platform.is("android")) {
              imageData = "file://" + "/storage/emulated/0/Download/Inspections/" + fileName
            }
            attachments.push(imageData)
            if (attachments.length > 4) {
              break;
            }
          }
        }
        if (attachments.length > 4) {
          break;
        }
      }

    }



    let email = {
      to: emailId,
      attachments: attachments,

      subject: 'Samson Inspection: ' + this.inspectionHeader.USER_UNIQUE_ID + " – " + today,
      body: message,
      isHtml: true
    }

    // Send a text message using default options
    this.emailComposer.open(email).then((result) => {
      console.log(result)
    });
  }

  goToLineTracker() {
    if (this.device.platform == 'browser') {
      if (!this.dissableAdd) {
        this.showDataSaveAlert("lineTracker")
      }
    } else {
      this.dataService.navigateToLineTracker(this)
    }
  }

  goToInspection() {
    if (this.device.platform == 'browser') {
      if (!this.dissableAdd) {
        this.showDataSaveAlert("inspection")
      }
    } else {
      this.dataService.navigateToInspection()
    }
  }

  async presentReportToast() {
    if (this.dissableAdd && this.dataService.showEmailFlag == true) {
      this.toastPresent = true;
      // Get the snackbar DIV
      var x = document.getElementById("snackbar");

      // Add the "show" class to DIV
      if(x!=undefined) {
        x.className = "show";
        x.setAttribute("visibility", "visible")
      }
    }

    // if(this.dissableAdd && this.dataService.showEmailFlag == true)  { 
    //   this.toastPresent = true;
    //   this.repToast = await this.toastController.create({
    //     message: this.translate.instant("To get more information on assistence for inspection check the last page of the report"),
    //     color: "dark",
    //     position: "bottom",
    //     cssClass: 'toastAfterHeader',
    //     buttons: [
    //       {
    //         text: 'Know More',
    //         role: 'cancel',
    //         handler: () => {
    //           console.log('Cancel clicked');
    //           this.repToast.dismiss()
    //         }
    //       }
    //     ]
    //   });
    //   this.repToast.onDidDismiss().then((data) => {
    //     this.showToastOnLoad = false;
    //     this.toastPresent = false;
    //   });
    //   this.repToast.present().then(()=> {
    //     this.showToastOnLoad = true;
    //   });
    // }
  }

  dismissToast() {
    var x = document.getElementById("snackbar");
    this.showToastOnLoad = false;
    setTimeout( () => {
      x.className = x.className.replace("show", "");
      x.setAttribute("visibility", "hidden")
      this.toastPresent = false;
    }, 1000);
    if (this.repToast != undefined) {
      this.repToast.dismiss()
    }
  }

  async showModal() {
    const modal = await this.modalController.create({
      component: AdvancedAssistencePage
    });
    await modal.present();
    modal.onDidDismiss().then(async (data) => {
      console.log(data);
      if (data != undefined) {
        if (data.data.requested == true) {
          this.reportDownloaded = true;
        }
      }
    });
  }

  async showAdvancedAssistenceAlert() {
    const alert = await this.alertController.create({
      message: '<p>Thank you for requesting an Enhanced Review of your inspection findings from Samson’s technical staff. This review will include feedback from Samson’s technical staff to help you determine cause and remedy to the damage captured in the inspection. You will now be taken to Samson’s payment gateway to transact this Enhanced Review request. Once the transaction is complete, Samson will respond within 2 business days with the results of our technical analysis and to deliver your report.</p>' + '<p>If you are an Icaria Premium member and have received this message, please contact your Samson representative as you require no payment for this service.</p>',
      backdropDismiss: false,
      buttons: [
        {
          text: this.translate.instant('OK'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: async (blah) => {
            await this.showAdvancedAssistencePage();
            if (this.device.platform == "browser") {
              this.deleteHeader(this.inspectionHeader)
            } else {
              this.dataService.refreshData();
            }

          }
        }
      ]
    });

    await alert.present();
  }

  async showAdvancedAssistenceInfoAlert() {
    const alert = await this.alertController.create({
      message: "Samson’s Enhanced Review allows you to simply report to us what you are seeing. We can take it from there. Samson will take the data, review, and report back what we think you should do at a small cost.",
      backdropDismiss: false,
      buttons: [
        {
          text: this.translate.instant('OK'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: (blah) => {
          }
        }
      ]
    });

    await alert.present();
  }

  async checkForPurchaceInformation() {
    console.log(this.inspectionHeader)
    var temp = JSON.parse(this.inspectionHeader.EXT_FLD1)
    console.log(this.inspectionHeader.EXT_FLD1)
    var user = await this.unviredCordovaSDK.dbSelect("USER_HEADER", '');
    if (user.type == ResultType.success) {
      if (user.data.length > 0) {
        this.selectedUser = user.data[0];
      }
    }
    if (this.selectedUser != undefined) {

      /**
       * 14 Sep 2021: This condition was added based on the following explanation by Chris.
       * I tested through logged in as a Samson employee. I was able to test through the island patos and Chevron test vessel. 
       * They are theoretically both Premium customers. 
       * One of the requirements for this project is that Premium customers do not do not have the option for Enhanced Inspection buy-up because it is included in their service package. 
       * Both test gave the option and allowed them to pay. How do we get this working?
       */
      if (this.selectedUser.ICARIA_PROGRAM == null || this.selectedUser.ICARIA_PROGRAM.toLowerCase().indexOf(('premium').toLowerCase()) < 0) {
        if (temp.CertExistsInSamsonSystem == 'yes') {
          if (this.inspectionHeader.CERTIFICATE_NUM != '' || this.inspectionHeader.CERTIFICATE_NUM != null) {
            var cert = await this.unviredCordovaSDK.dbSelect("CERTIFICATE_HEADER", "CERTIFICATE_NUM = '" + this.inspectionHeader.CERTIFICATE_NUM + "'")
            if (cert.type == ResultType.success) {
              if (cert.data.length > 0) {
                if (cert.data[0].COMPETITOR_CERT != 'X') {
                  console.log(" true ")
                  this.showPurchaseOption = true
                } else {
                  console.log(" false ")
                  this.showPurchaseOption = false
                }
              }
            }
          }
        } else {
          if (temp.IsSamsonProduct == "yes") {
            console.log(" true ")
            this.showPurchaseOption = true
          } else {
            console.log(" false ")
            this.showPurchaseOption = false
          }
        }
      }
    }

    console.log(temp)
  }

  resetPermission(event) {
    event.preventDefault();
    event.stopPropagation();
    console.log(event)
    if (this.inspectionHeader.ADV_SUPRT_STATUS != AppConstant.REQUESTED) {
      this.inspectionHeader.ADV_SUPRT_STATUS != 'ERROR'
      if (event.detail.checked == true) {
        this.disableAdvancedPayment = false;
      } else {
        this.disableAdvancedPayment = true;
      }
    }
  }

  stopEvent() {
    event.preventDefault();
    event.stopPropagation();
  }

  async showAdvancedAssistencePage() {
    var url = "";
    var uri_dec = "";
    this.alertService.present();
    var inputHeader = {
      PAGE_NAME: "Payments"
    }
    let inputObject = {
      "INPUT_GET_CONNECT_URL": [
        {
          "INPUT_GET_CONNECT_URL_HEADER": inputHeader
        }]
    }
    var temp = await this.unviredCordovaSDK.syncForeground(RequestType.QUERY, "", inputObject, "ROPE_INSPECTIONS_PA_GET_CONNECT_URL", false)
    console.log("SYNC Result " + JSON.stringify(temp))
    if (temp.type == ResultType.error) {
      this.alertService.showAlert("Error", temp.message)
      this.unviredCordovaSDK.logError("Contact", "send message ", JSON.stringify(temp))
      this.alertService.dismiss();
    } else {
      url = temp.data.url
      console.log(temp)
      var decodeString = temp.data
      var decodedJaon = JSON.parse(decodeString)
      url = decodedJaon.pageURL;
      uri_dec = url + "?inspection_id=" + this.inspectionHeader.INSPECTION_ID + "&customer_number=" + this.selectedUser.ID + "&Product_code=" + this.inspectionHeader.PRODUCT_CODE;



      // var uri_dec = "http://samsonrope.biz?inspection_id=" + this.inspectionHeader.INSPECTION_ID + "&customer_number=" + this.selectedUser.ID + "&Product_code=" + this.inspectionHeader.PRODUCT;
      if (this.disableAdvancedPayment == false && uri_dec != '') {
        if (this.device.platform == "browser") {
          browser = this.iab.create(uri_dec, "_system", "");
          if (browser != null) {
            browser.show();
          }
          this.alertService.dismiss();
          browser.on('exit').subscribe(event => {
            console.log("exit " + event)
            // this.updateInspectionPaymentStatus(this.inspectionHeader)
            // this.setPortrait()
          });
          browser.on('loadstart').subscribe(event => {
            console.log(event);
            if (event && event.url) {
              if (event.url.includes('/success.html?')) {
                setTimeout(() => {
                  browser.close();
                }, 5000);
              }
            }
            if (browser != null) {
              browser.show();
            }
          });
        } else {
          this.unviredCordovaSDK.logInfo("OBSERVATION", "SHOWADVANCEASSISTENCEPAGE", "URI_DEC ==== " + uri_dec)
          var browser = this.iab.create(uri_dec, "_blank", "");
          if (browser != null) {
            setTimeout(() => {
              browser.show()
            }, 5000);
            this.unviredCordovaSDK.logInfo("OBSERVATION", "SHOWADVANCEASSISTENCEPAGE", "displayed URI_DEC ==== " + uri_dec)
            setTimeout(() => {
              this.alertService.dismiss();
            }, 15000);
          }
          browser.on('exit').subscribe(event => {
            console.log("exit " + event)
            this.unviredCordovaSDK.logInfo("OBSERVATION", "SHOWADVANCEASSISTENCEPAGE", "ON EXIT" + JSON.stringify(event))
            this.updateInspectionPaymentStatus(this.inspectionHeader)
            this.setPortrait()
          });
          browser.on('loaderror').subscribe(event => {
            console.log("loaderror " + event)
            this.unviredCordovaSDK.logInfo("OBSERVATION", "SHOWADVANCEASSISTENCEPAGE", "ON loaderror   ===" + JSON.stringify(event))
            this.updateInspectionPaymentStatus(this.inspectionHeader)
            this.setPortrait()
          });
          browser.on('loadstop').subscribe(event => {
            console.log("loadstop " + event)
            this.unviredCordovaSDK.logInfo("OBSERVATION", "SHOWADVANCEASSISTENCEPAGE", "ON loadstop   ===" + JSON.stringify(event))
            this.updateInspectionPaymentStatus(this.inspectionHeader)
            this.setPortrait()
          });
          browser.on('loadstart').subscribe(event => {
            console.log(event);
            this.unviredCordovaSDK.logInfo("OBSERVATION", "SHOWADVANCEASSISTENCEPAGE", "ON LOAD START" + JSON.stringify(event))

            if (event && event.url) {
              if (event.url.includes('/success.html?')) {
                setTimeout(() => {
                  browser.close();
                }, 5000);
              }
            }
            if (browser != null) {
              browser.show();
            }
          });
        }
        this.unlockScreen()
        this.presentToast()
        this.service.setInspState('open');
        this.unviredCordovaSDK.logInfo("OBSERVATION", "SHOWADVANCEASSISTENCEPAGE", "NAVIGATING BACK")
        setTimeout(() => {
          this.navController.navigateBack(['inspection'])
        }, 5000);
        this.alertService.dismiss();
      }
    }
  }

  setPortrait() {
    // set to portrait
    this.screenOrientation.lock(this.screenOrientation.ORIENTATIONS.PORTRAIT);
  }

  unlockScreen() {
    // allow user rotate
    this.screenOrientation.unlock();
  }

  async getLoggedInUser() {
    var res = await this.unviredCordovaSDK.dbSelect("USER_HEADER", '')
    if (res.type == ResultType.success) {
      if (res.data.length > 0) {
        this.selectedUser = res.data[0];
      }
    }
  }

  checkIfSelected() {
    return !this.disableAdvancedPayment;
  }

  deleteHeader(inspectionHeader) {
    var res = this.unviredCordovaSDK.dbDelete("INSPECTION_HEADER", "INSPECTION_ID = '" + inspectionHeader.INSPECTION_ID + "'")
    if (this.device.platform == "browser") {
      this.unviredCordovaSDK.dbSaveWebData();
    }
  }

  async showDataSaveAlert(mode) {
    var dataAlert = await this.alertController.create({
      backdropDismiss: false,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('Save Changes'),
      message: '<strong>' + this.translate.instant('The inspection is not yet completed.  You will lose the data if the browser is closed.  Save and proceed?') + '</strong>',
      buttons: [
        {
          text: this.translate.instant('Yes'),
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            this.saveDataAndGoBack(mode)
          }
        }, {
          text: this.translate.instant('No'),
          handler: () => {

          }
        }
      ]
    });
    await dataAlert.present();
  }

  saveDataAndGoBack(mode) {
    this.unviredCordovaSDK.dbSaveWebData();
    switch (mode) {
      case "backButton":
        if (this.service.getFromfromInspection()) {
          setTimeout(() => {
            this.navController.navigateBack('/inspection')
          }, 1000);
        } else {
          setTimeout(() => {
            this.navController.navigateBack('/inspection-home')
          }, 1000);
        }
        break;
      case "lineTracker":
        this.dataService.navigateToLineTracker(this)
        break;
      case "inspection":
        this.dataService.navigateToInspection();
        break;
    }
  }

  async updateInspectionPaymentStatus(success) {
    // if(success) {
    // this.inspectionHeader.PAYMENT_STATUS = AppConstant.PAYMENT_COMPLETED
    // await this.unviredCordovaSDK.dbUpdate(AppConstant.TABLE_INSPECTION_HEADER, this.inspectionHeader, `EXTERNAL_ID = '${this.inspectionHeader.INSPECTION_ID}' OR EXTERNAL_ID = '${this.inspectionHeader.EXTERNAL_ID}'`)
    // }
  }

  editConfiguration() {
    this.route.navigate(['inspection-configuration'])
  }

  async Edit(type: string, index: number, mode: string) {

    let page = '';
    let props;
    var new_obj = this.labEmployeeObservationList[index];
    this.readFlag = this.service.getInspectionReadFlag();
    if (this.readFlag === 'readOnly') {
      this.service.setObservationReadFlag(this.readFlag);
    }
    if(mode == 'edit') {
      if (this.labEmployeeObservationList[index].DATA != undefined && this.labEmployeeObservationList[index].DATA != '') {
        this.service.setMeasurementEditMode(true);
          let itemCopy = JSON.parse(JSON.stringify(this.labEmployeeObservationList[index].DATA));
          this.service.setExternalData(itemCopy);
          if(this.platformId === 'electron') {
            let extImages = JSON.parse(JSON.stringify(itemCopy.externalImage))
            let orgImages =JSON.parse(JSON.stringify(itemCopy.originalImages))
            await this.cameraService.setImages(extImages,orgImages)
          } else {
            await this.cameraService.setImages(itemCopy.externalImage,itemCopy.originalImages)
          }
      } else {
        this.service.setMeasurementEditMode(false);
      }
    } else {
      this.service.setMeasurementEditMode(false);
      // if (this.labEmployeeObservationList[index].DATA != undefined && this.labEmployeeObservationList[index].DATA != '')  {
        new_obj = { specimen: this.labEmployeeObservationList.length + 1, type: '', start: '', end: '', notes: '', typeValue: type, DATA: '', photos:[]}
      // }
    }
    if(this.dataService.selectedSegment == '' || this.dataService.selectedSegment == null || this.dataService.selectedSegment == undefined) {
      this.dataService.selectedSegment = 'length';
    }

    switch (type) {
      case 'external':
        props = { ropeEnd: 'A', isEmpEditConfig: true, typeIndex: index, endType: 'External Abrasion' };
        if(new_obj.type == '') {
          new_obj.type = "External Abrasion"
        }
        await this.presentModal(ExternalAbrasionPage, props);
        break;
      case 'internal':
        props = { ropeEnd: 'A', isEmpEditConfig: true, typeIndex: index, endType: 'Internal Abrasion' };
        if(new_obj.type == '') {
          new_obj.type = "Internal Abrasion"
        }
        await this.presentModal(InternalAbrasionPage, props);
        break;
      case 'cuts':
        props = { ropeEnd: 'A', isEmpEditConfig: true, typeIndex: index, endType: 'Cut Strands' };
        if(new_obj.type == '') {
          new_obj.type = "Cut Strands"
        }
        await this.presentModal(CutsPage, props);
        break;
      case 'twist':
        props = { ropeEnd: 'A', isEmpEditConfig: true, typeIndex: index, endType: 'Twist' };
        if(new_obj.type == '') {
          new_obj.type = "Twist"
        }
        await this.presentModal(TwistPage, props);
        break;
      case 'compression':
        props = { ropeEnd: 'A', isEmpEditConfig: true, typeIndex: index, endType: 'Compression' };
        if(new_obj.type == '') {
          new_obj.type = "Compression"
        }
        await this.presentModal(CompressionPage, props);
        break;
      case 'melting':
        props = { ropeEnd: 'A', isEmpEditConfig: true, typeIndex: index, endType: 'Melting' };
        if(new_obj.type == '') {
          new_obj.type = "Melting"
        }
        await this.presentModal(MeltingPage, props);
        break;
      case 'contamination':
        props = { ropeEnd: 'A', isEmpEditConfig: true, typeIndex: index, endType: 'Contamination' };
        if(new_obj.type == '') {
          new_obj.type = "Contamination"
        }
        await this.presentModal(ContaminationPage, props);
        break;
      case 'pulled':
        props = { ropeEnd: 'A', isEmpEditConfig: true, typeIndex: index, endType: 'Pulled Strands' };
        if(new_obj.type == '') {
          new_obj.type = "Pulled Strands"
        }
        await this.presentModal(PulledPage, props);
        break;
      case 'discoloration':
        props = { ropeEnd: 'A', isEmpEditConfig: true, typeIndex: index, endType: 'Discoloration' };
        if(new_obj.type == '') {
          new_obj.type = "Discoloration"
        }
        await this.presentModal(DiscolorationPage, props);
        break;
      case 'inconsistent diameter':
        props = { ropeEnd: 'A', isEmpEditConfig: true, typeIndex: index, endType: 'Incinsistent Diameter' };
        if(new_obj.type == '') {
          new_obj.type = "Incinsistent Diameter"
        }
        await this.presentModal(InconsistentDiameterPage, props);
        break;
      case 'linear damage':
        props = { isEmpEditConfig: true, typeIndex: index, endType: 'Linear Damage' };
        if(new_obj.type == '') {
          new_obj.type = "Linear Damage"
        }
        await this.presentModal(LinearDamagePage, props);
        break;
      case 'kinked yarns':
        props = { isEmpEditConfig: true, typeIndex: index, endType: 'Kinked Yarns' };
        if(new_obj.type == '') {
          new_obj.type = 'Kinked Yarns'
        }
        await this.presentModal(KinkingPage, props);
        break;
      case 'parting':
        props = { isEmpEditConfig: true, typeIndex: index, endType: 'Parting' };
        if(new_obj.type == '') {
          new_obj.type = "Parting"
        }
        await this.presentModal(PartingPage, props);
        break;
    }
    if( new_obj.specimen > this.labEmployeeObservationList.length ) {
      this.labEmployeeObservationList.push(new_obj);
    }
  }

  async presentModal(component: any, componentProps: any) {
    this.alertService.present().then(async () => {
      const modal = await this.modalController.create({
        component: component,
        componentProps: componentProps,
        cssClass: 'customConfiguration',
        backdropDismiss: false
      });
      await modal.present().then(async res => {
        if (this.alertService.isLoading) {
          await this.alertService.dismiss()
        }
      });

      modal.onDidDismiss().then(async (data) => {
        // console.log(data);
        await this.refreshData();
        this.baselineArray.forEach(config => {
          // let index;
          if(this.labEmployeeObservationList.length > 10)   {
            console.log('remove additionallyadded')
          }
          switch (config.DATA.type) {
            case 'Internal Abrasion':
              console.log(config)
              break;
          }
        })
      });
    })
  }

  toggleSpeciman() {
    this.isSpecimanGridOpen = !this.isSpecimanGridOpen;
  }

  checkSpecimanSectionIsOpen() {
    return this.isSpecimanGridOpen
  }

  toggleSample() {
    this.isSampleGridOpen = !this.isSampleGridOpen;
  }

  checkSampleSectionIsOpen() {
    return this.isSampleGridOpen
  }

  async addSpecimen(isSpecimenEdit?:boolean,index?:number) {
    let componentProps;
    if(isSpecimenEdit!=undefined && isSpecimenEdit) {
      this.labEmployeeSpecimenslist[index].LMD_ID;
      let res = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_LMD_HEADER,`LMD_ID = '${this.labEmployeeSpecimenslist[index].LMD_ID}'`);
      if(res.type==ResultType.success) {
        componentProps = { value: res.data[0], title: 'Add Specimen', page: 'Specimen', isSpecimenEdit:true };
        if(this.dissableAdd || this.inspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED && this.labEmployeeSpecimenslist[index].OBJECT_STATUS!=1 && this.labEmployeeSpecimenslist[index].OBJECT_STATUS!=2) {
          componentProps['disableAdd'] = true;
        }
      } else {
        console.log("error reading LMD_HEADER");
      }
      
    } else {
      componentProps = { value: '', title: 'Add Specimen', page: 'Specimen' }
    }
    const modal = await this.modalController.create({
      component: GenericListPage,
      componentProps: componentProps,
      backdropDismiss: false,
      cssClass: 'specimenModal'
    });
    await modal.present();
    modal.onDidDismiss().then(async (data) => {
      // console.log(data);
      await this.refreshData()
    });
  }

  async deleteSpecimen(specimenNum:any,index:any) {
    let role = await this.alertService.showAlertModal("Warning!","Are you sure you want to delete this specimen?");
    if(role=='continue') {
      await this.alertService.preasentLoading();
      this.labEmployeeSpecimenslist[index].LMD_ID;
      let inspId = (this.inspectionHeader.INSPECTION_STATUS==AppConstant.REOPENED) ? this.inspectionHeader.EXTERNAL_ID : this.inspectionHeader.INSPECTION_ID;
      let specimenRes = await this.unviredCordovaSDK.dbDelete('LMD_HEADER',`LMD_ID =  '${this.labEmployeeSpecimenslist[index].LMD_ID}' AND EXTFLD1 = '${ inspId }'`);
      if(specimenRes.type == ResultType.success) {
        console.log("lmd delition success");
        await this.selectSpecimens();
        if(this.alertService.isLoading) {
          try {
            await this.alertService.dismiss();
          } catch(err) {
            console.log(err)
          }
        }
      } else {
        if(this.alertService.isLoading) {
          await this.alertService.dismiss()
        }
        console.log("error deleting lmd from db")
      }
    } else {
      console.log("dont delete");
    }
  }

  async getImage(image: any): Promise<SafeUrl> {
    let url: SafeUrl = this.domSanitizer.bypassSecurityTrustUrl(image?.Image?.changingThisBreaksApplicationSecurity);
    return url;
  }

  editInspection() {
    if(this.isLoggedInUserEmployee) {
      this.route.navigate(['create-inspection']);
    }
  }

  async presentPopover(type: string, index: number, mode: string,event:Event) {
    let isUtilTenex = false;
    if(this.isUtility && this.inspectionHeader.PRODUCT_TYPE == 'Conventional Fiber (Class I)' && this.inspectionHeader.CONSTRUCTION == "12-Strand") {
      isUtilTenex = true;
    }
    const popover = await this.popoverController.create({
      component: AbrasionListPage,
      componentProps: { isTenex: isUtilTenex },
      event: event,
      showBackdrop: true,
      animated: true
    });
    await popover.present();
    popover.onDidDismiss().then(async (data) => {
      if(data.data != undefined) {
        this.Edit(data.data,index,mode)
      }
    });
  }
  
  // async presentAlertLmd() {
  //   const alert = await this.alertController.create({
  //     message: '<strong>Would you like to document Repair?</strong>',
  //     buttons: [
  //       {
  //         text: this.translate.instant('No'),
  //         role: 'cancel',
  //         cssClass: 'secondary',
  //         handler: (blah) => {

  //         }
  //       }, {
  //         text: this.translate.instant('Yes'),
  //         handler: async () => {
  //           await this.navigateToNewLMD();
  //         }
  //       }
  //     ]
  //   });

  //   await alert.present();
  // }

  // async navigateToNewLMD() {
  //   this.lmdService.setReadOnlyMode(false);
  //   this.service.setLMDEditMode(false)
  //   this.lmdService.setInspectionMode(true);
  //   this.lmdService.setInspection(this.inspectionHeader)
  //   var workorder = {};
  //   if(this.inspectionHeader.WORK_ORDER != '' && this.inspectionHeader.WORK_ORDER != null && this.inspectionHeader.WORK_ORDER != undefined) {
  //     var tempWo = await this.unviredCordovaSDK.dbSelect("WORK_ORDER_HEADER", `ID = '${this.inspectionHeader.WORK_ORDER}'`)
  //     if(tempWo.type == ResultType.success) {
  //       if (tempWo.data.length > 0) {
  //         workorder = tempWo.data[0]
  //       } else {
  //         workorder =  {
  //           "WO_NUMBER" : this.inspectionHeader.WORK_ORDER,
  //           "WO_INTERNAL" : this.inspectionHeader.WORK_ORDER,
  //           "ID" : this.inspectionHeader.WORK_ORDER
  //         }
  //       } 
  //     } else {
  //       workorder =  {
  //         "WO_NUMBER" : this.inspectionHeader.WORK_ORDER,
  //         "WO_INTERNAL" : this.inspectionHeader.WORK_ORDER,
  //         "ID" : this.inspectionHeader.WORK_ORDER
  //       }
  //     }      
  //   } else if (this.inspectionHeader.CUSTOMER_WORK_ORDER != '' && this.inspectionHeader.CUSTOMER_WORK_ORDER != null && this.inspectionHeader.CUSTOMER_WORK_ORDER != undefined) {
  //     workorder =  {
  //       "WO_NUMBER" : this.inspectionHeader.CUSTOMER_WORK_ORDER,
  //       "WO_INTERNAL" : this.inspectionHeader.CUSTOMER_WORK_ORDER,
  //       "ID" : this.inspectionHeader.WORK_ORDER
  //     }
  //   }
  //   this.lmdService.setSelectedLMDWorkorder(workorder)
  //   this.lmdService.setIsFromCompleted(false);
  //   this.lmdService.selectedInspection = this.inspectionHeader;
  //   this.lmdService.selectedCertNo = this.inspectionHeader.CERTIFICATE_NUM
  //   this.route.navigate(['new-lmd']);
       
  // }

  // async presentPopoverMenu() {
  //   const popover = await this.popoverController.create({
  //     component: HomePopupPage,
  //     componentProps: { page: this, insightAiEnabled: this.dataService.isUserEnabledForInsightAI, openedPage: 'observation' },
  //     event,
  //     showBackdrop: true,
  //     animated: true
  //   });
  //   await popover.present();
  //   popover.onDidDismiss().then(async (data) => {
  //     switch (data.data) {
  //       case 5:
  //         this.helpService.helpMode ? this.test() : this.presentAlertLmd()
  //         break;
  //       case 6:
  //         this.helpService.helpMode ? this.test() : this.presentAlertConfirm()
  //         break;
  //     }
  //   });
  // }
}