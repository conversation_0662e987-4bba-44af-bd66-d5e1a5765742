import { Injectable } from '@angular/core';
import { UnviredCordovaSDK, ResultType, RequestType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstant } from '../../constants/appConstants';
import { USER_HEADER } from '../../models/USER_HEADER';
import { MEASUREMENT } from '../../models/MEASUREMENT';
import { INSPECTION_HEADER } from '../../models/INSPECTION_HEADER';
import { INSPECTION_ATTACHMENT } from '../../models/INSPECTION_ATTACHMENT';
import { LMD_ATTACHMENT } from '../../models/LMD_ATTACHMENT';
import { UtilserviceService } from './utilservice.service';
import { ANNOTATION } from '../../models/ANNOTATION';
import { CameraService } from './camera.service';
import { Platform, AlertController, NavController } from '@ionic/angular';
import { AlertService } from './alert.service';
import { CONFIGURATION } from '../../models/CONFIGURATION';
import { APP_SETTINGS_HEADER } from '../../models/APP_SETTING_HEADER';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { UserPreferenceService } from './user-preference.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { ScreenOrientation } from '@awesome-cordova-plugins/screen-orientation/ngx';
// import {v4 as uuid} from 'uuid';
import * as moment from 'moment';
import { NgxImageCompressService } from 'ngx-image-compress';
import { Geolocation } from '@awesome-cordova-plugins/geolocation/ngx';
import { LocationAccuracy } from '@awesome-cordova-plugins/location-accuracy/ngx';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { FileOpener } from '@awesome-cordova-plugins/file-opener/ngx';
import { PlatformService } from './platform.service';

@Injectable({
  providedIn: 'root'
})
export class DataService {
  platformId: string = this.platformService.getPlatformId();
  insertedHeader: INSPECTION_HEADER;
  srNo: number = 0;
  selectedUom: string = 'm';
  selectedUomString: string = 'meters';
  selectedUomStringTwist: string = "Meters"
  selectedRoutineInspectionlengthUomString : string = 'Meters'
  selectedRoutineInspectionDiamUomString: string = 'mm'
  selectedLMDUomString: string = 'meters'
  refreshTimeout: any = null;
  firstLogIn: boolean = false;
  browserFirstLogIn: boolean = false;
  refreshDataTimeout: any = null;
  w_refreshDataTimeout: any = null;
  clearRefreshTimeout: any = null;
  locationOptions: any = [];
  configLocationOptions: any = [];
  layerOptions: any[];
  refreshCalled: boolean = false;
  getCustomizationCalled: boolean = false;
  selectedInspectionType: string = '';
  loadBearingOption: any;
  selectedSegment: any;
  measurementChanged: boolean = false;
  legOptionsList: any;
  configlegOptionsList: any;
  layerOptionsImage: any
  showEmailFlag: boolean = false;
  isConnectedToNetwork: boolean = false;
  currentScreenOrientation: any;
  redirectPage: string = '';
  showSaveDB: boolean = false;
  selectedRole: string;

  lastUsedAccount: any;
  lastUsedAsset: any;
  lastUsedWorkOrder: any;
  lastUsedWorkOrderSelection: any;
  lastUsedCustomWorkOrder:any;
  configurationOption: any;
  loggedinUrl: any;
  public accountList;
  public assetList;
  public workorderList;
  public certLList;
  public assetId = '';
  public accountId = '';
  public imageurlInspection: string = "../../assets/img/MainMenu_Inspections.png"
  public imageUrlInspectionWindows: string = "./assets/img/winImg/op-imgs/Main-Menu_Inspections.png";
  public imageurlLineManagement: string ="../../assets/img/MainMenu_LineTracker.png"
  public imageurlLineManagementWindows: string = "./assets/img/winImg/op-imgs/Main-Menu_LineTracker.png"
  public imageUrlDetailedInspection:string = "../../assets/img/MainMenu_Inspections.png";
  public imageUrlDetailedInspectionWin:string = "./assets/img/winImg/op-imgs/Main-Menu_Inspections.png";
  public imageUrlRoutineInspection :string = "../../assets/img/New_Inspection.png";
  public imageUrlRoutineInspectionWin:string= "./assets/img/winImg/op-imgs/Inspections_New_2024x2024.png";
  public imageUrlInspectionNew:string = "../../assets/img/New_Inspection.png"
  public imageUrlInspectionNewWin:string = "./assets/img/winImg/op-imgs/Inspections_New_2024x2024.png";
  public reportingLineHistory:string= "../../assets/img/LineTracker_Maintenance.png";
  public reportingLineHistoryWin:string= "./assets/img/winImg/op-imgs/Reporting_Line-History_2024x2024.png";
  public reportingUsageHistory:string= "../../assets/img/LIneTracker_MooringHistory.png";
  public reportingUsageHistoryWin:string= "./assets/img/winImg/op-imgs/Reporting_Usage-History_2024x2024.png";
  public imageUrlInspInProgress:string = "../../assets/img/Inspections InProgress.png";
  public imageUrlInspInProgressWin:string = "./assets/img/winImg/op-imgs/Inspections_InProgress_2024x2024.png";
  public isUserEnabledForInsightAI: boolean = false;
  public selectedIndustryType:string='';
  constructor(
    public unviredCordovaSDK: UnviredCordovaSDK,
    public platformService: PlatformService,
    public alertController: AlertController,
    private serveice: UtilserviceService,
    public cameraService: CameraService,
    public userPreferenceService: UserPreferenceService,
    private platform: Platform,
    public navCtrl: NavController,
    public translate: TranslateService,
    public alertService: AlertService,
    public router: Router,
    public device: Device,
    public iab: InAppBrowser,
    public fileOpener: FileOpener,
    private screenOrientation: ScreenOrientation,
    private file:File) {
    this.isConnectedToNetwork = navigator.onLine
    this.currentScreenOrientation = this.screenOrientation.type;
    this.loggedinUrl = AppConstant.URL;
  }

  async getUserHeader(): Promise<any> {
    return await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_USER_HEADER, '')
  }

  setCreatedHeader(header: any) {
    this.insertedHeader = header;
  }

  async setInitialImages() {
    var isworkboat = false
    let utility:boolean = false;
    var selectedIndustry = await this.userPreferenceService.getUserPreference('industry');
    if (selectedIndustry != undefined && selectedIndustry != "undefined" && selectedIndustry != null && selectedIndustry != '' && selectedIndustry != '""') {
      selectedIndustry = JSON.parse(selectedIndustry)
      this.selectedIndustryType = selectedIndustry.ID;
      if(selectedIndustry.ID.includes("Workboat")) {
        this.selectedIndustryType = 'workboat'
        isworkboat = true;
      } else if(selectedIndustry.ID.includes("Utility")) {
        utility = true;
        this.selectedIndustryType = 'Utility';
      }
    }
    if(isworkboat == true) {
        this.imageurlInspection = "../../assets/img/Inspections_Tug_mobile_device.png"
        this.imageurlLineManagement = "../../assets/img/LineManagement_Tug_mobile_device.png"
        this.imageUrlInspectionWindows = "./assets/img/winImg/op-imgs/Inspections_Tug_Windows_Device.png"
        this.imageurlLineManagementWindows = "./assets/img/winImg/op-imgs/LineManagement_Tug_Windows_Device.png"
      } else if(utility) {
        this.imageurlInspection = "../../assets/img/Utility_Inspections.jpg"
        this.imageurlLineManagement ="../../assets/img/Utility_LineTracker.JPG"
        this.imageUrlInspectionWindows = "./assets/img/winImg/op-imgs/Utility_Inspections.jpg"
        this.imageurlLineManagementWindows = "./assets/img/winImg/op-imgs/Utility_LineTracker.JPG"
        this.imageUrlDetailedInspection = "../../assets/img/Utility_Detailed-Inspection.jpg"
        this.imageUrlDetailedInspectionWin = "./assets/img/winImg/op-imgs/Utility_Detailed-Inspection.jpg"
        this.imageUrlRoutineInspection = "../../assets/img/Utility_Routine-Inspection.jpg"
        this.imageUrlRoutineInspectionWin = "./assets/img/winImg/op-imgs/Utility_Routine-Inspection.jpg"
        this.imageUrlInspectionNew= "../../assets/img/Utility_New-Inspection.jpg";
        this.imageUrlInspectionNewWin = "./assets/img/winImg/op-imgs/Utility_New-Inspection.jpg";
        this.reportingLineHistory= "../../assets/img/Utility_Reporting_Line-History.jpg";
        this.reportingLineHistoryWin= "./assets/img/winImg/op-imgs/Utility_Reporting_Line-History.jpg";
        this.reportingUsageHistory= "../../assets/img/Utility_Reporting_Usage_History.jpg";
        this.reportingUsageHistoryWin= "./assets/img/winImg/op-imgs/Utility_Reporting_usage_History.jpg";
        this.imageUrlInspInProgress= "../../assets/img/Utility_In progress Inspection.png";
        this.imageUrlInspInProgressWin= "./assets/img/winImg/op-imgs/Utility_In progress Inspection.jpg";
      } else {
        this.imageurlInspection = "../../assets/img/MainMenu_Inspections.png"
        this.imageurlLineManagement ="../../assets/img/MainMenu_LineTracker.png"
        this.imageUrlInspectionWindows = "./assets/img/winImg/op-imgs/Main-Menu_Inspections.png"
        this.imageurlLineManagementWindows = "./assets/img/winImg/op-imgs/Main-Menu_LineTracker.png"
      }
      if(!utility) {
        this.imageUrlDetailedInspection = "../../assets/img/MainMenu_Inspections.png";
        this.imageUrlDetailedInspectionWin = "./assets/img/winImg/op-imgs/Main-Menu_Inspections.png";
        this.imageUrlRoutineInspection = "../../assets/img/New_Inspection.png";
        this.imageUrlRoutineInspectionWin= "./assets/img/winImg/op-imgs/Inspections_New_2024x2024.png";
        this.imageUrlInspectionNew = "../../assets/img/New_Inspection.png"
        this.imageUrlInspectionNewWin = "./assets/img/winImg/op-imgs/Inspections_New_2024x2024.png";
        this.reportingLineHistory= "../../assets/img/LineTracker_Maintenance.png";
        this.reportingLineHistoryWin= "./assets/img/winImg/op-imgs/Reporting_Line-History_2024x2024.png";
        this.reportingUsageHistory= "../../assets/img/LIneTracker_MooringHistory.png";
        this.reportingUsageHistoryWin= "./assets/img/winImg/op-imgs/Reporting_Usage-History_2024x2024.png";
        this.imageUrlInspInProgress = "../../assets/img/Inspections InProgress.png";
        this.imageUrlInspInProgressWin = "./assets/img/winImg/op-imgs/Inspections_InProgress_2024x2024.png";
      }
      return;
  }

  getCreatedHeader(header: any) {
    return this.insertedHeader;
  }

  async saveMeasurements(measurementJson: any, update?: boolean, noReset?: boolean, createAttachment?: boolean): Promise<any> {
    var temp = false, resetFlag = false;
    if (noReset == true) {
      resetFlag = true;
    }
    if (update == true) {
      temp = true;
    }
    if (createAttachment == undefined) {
      createAttachment = true;
    }
    var selectedInspHeader = await this.getSelectedHeaderFromDb(this.insertedHeader.INSPECTION_ID)
    if (selectedInspHeader.type == ResultType.success) {
      if (measurementJson.id != undefined && measurementJson.id != "") {
        console.log("selected item =============" + JSON.stringify(selectedInspHeader.data))
        if (selectedInspHeader.data.length > 0) {
          if(measurementJson.CreatedDate == undefined || measurementJson.CreatedDate == null || measurementJson.CreatedDate == '') {
            if(this.device.platform == "iOS") {
              measurementJson["CreatedDate"] = moment(this.insertedHeader.CREATED_DATE).format('MM-DD-YYYY');
            } else {
              measurementJson["CreatedDate"] = moment(this.insertedHeader.CREATED_DATE.slice(0, 10)).format('MM-DD-YYYY'); 
            }
          }  else {
            if(this.device.platform == "iOS") {
              measurementJson.CreatedDate = measurementJson.CreatedDate
            } else {
              measurementJson.CreatedDate = moment(measurementJson.CreatedDate).format('MM-DD-YYYY'); 
            }
          }    
          if(measurementJson.EventDate == undefined || measurementJson.EventDate == null || measurementJson.EventDate == '') {
            if(this.device.platform == "iOS") {
              measurementJson["EventDate"] = this.insertedHeader.INSPECTION_DATE;
            } else {
              measurementJson["EventDate"] = moment(this.insertedHeader.INSPECTION_DATE.slice(0, 10)).format('MM-DD-YYYY'); 
            }
          }  else {
            if(this.device.platform == "iOS") {
              measurementJson.EventDate = measurementJson.EventDate; 
            } else {
              measurementJson.EventDate = moment(measurementJson.EventDate).format('MM-DD-YYYY'); 
            }
          } 
          var measurementItem = new MEASUREMENT();
          measurementItem.INSPECTION_ID = this.insertedHeader.INSPECTION_ID;
          measurementItem.MEAS_ID = measurementJson.id;
          measurementItem.MEAS_TYPE_ID = measurementJson.type;
          measurementItem.FID = selectedInspHeader.data[0].LID;
          measurementItem.MEAS_TIMESTAMP = new Date().getTime();
          var jsonString = JSON.stringify(measurementJson);
          measurementItem.DATA = jsonString;
          if(this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED) {
            measurementItem.U_MODE = 'A';
          }
          
          // if (temp == false) {
          //   measurementItem.MEAS_TIMESTAMP = new Date().getTime();
          // } else {
            // var updatingMeas = await this.unviredCordovaSDK.dbSelect("MEASUREMENT", "MEAS_ID like '" + measurementJson.id + "'")
            // if (updatingMeas.type == ResultType.success) {
            //   if (updatingMeas.data.length > 0) {
            //     measurementItem.MEAS_TIMESTAMP = updatingMeas.data[0].MEAS_TIMESTAMP;
            //   } else {
            //     measurementItem.MEAS_TIMESTAMP = new Date().getTime();
            //   }
            // } else {
            //   measurementItem.MEAS_TIMESTAMP = new Date().getTime();
            // }
          // }
          let measUpdate = await this.unviredCordovaSDK.dbSelect("MEASUREMENT", `INSPECTION_ID='${this.insertedHeader.INSPECTION_ID}' AND DATA LIKE '%"id":"${measurementJson.id}"%'`);
            if(measUpdate.type == ResultType.success) {
              if(measUpdate.data.length > 0) {
                measurementItem = measUpdate.data[0];
                measurementItem.DATA = jsonString;
                measurementItem.MEAS_TIMESTAMP = new Date().getTime();
                if(this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED && measurementItem.OBJECT_STATUS==0) {
                  measurementItem.OBJECT_STATUS = 2;
                  measurementItem.U_MODE = 'U';
                }
              }
            }
          // selectedHeader = await this.createAttachmentItem(measurementJson, selectedHeader.data[0].LID)
          var selectedHeader = await this.unviredCordovaSDK.dbInsertOrUpdate(AppConstant.TABLE_MEASUREMENT_ITEM, measurementItem, false)
          if (createAttachment == true) {
            let fileNames =[]
            let existingAttchments = [];
            if(this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED) {
              if(measurementJson.externalImage.length>0) {
                for(let i=0;i<measurementJson.externalImage.length;i++) {
                  let fileName = measurementJson.externalImage[i].Image.changingThisBreaksApplicationSecurity.substring(measurementJson.externalImage[i].Image.changingThisBreaksApplicationSecurity.lastIndexOf('/') + 1);
                  fileNames.push(fileName);
                  if(measurementJson.externalImage[i]?.tiledImages?.length>0) {
                    for(let t=0;t<measurementJson.externalImage[i]?.tiledImages?.length;t++) {
                      fileName='';
                      fileName = measurementJson.externalImage[i]?.tiledImages[t].Image.changingThisBreaksApplicationSecurity.substring(measurementJson.externalImage[i]?.tiledImages[t].Image.changingThisBreaksApplicationSecurity.lastIndexOf('/') + 1);
                      fileNames.push(fileName);
                    }
                  }
                }
              }

              if(measurementJson.originalImages.originalImgList.length>0) {
                for(let i=0;i<measurementJson.originalImages.originalImgList.length;i++) {
                  let fileName = measurementJson.originalImages.originalImgList[i].originalImgList.changingThisBreaksApplicationSecurity.substring(measurementJson.originalImages.originalImgList[i].originalImgList.changingThisBreaksApplicationSecurity.lastIndexOf('/') + 1);
                  fileNames.push(fileName);
                }
              }

              // if(measurementJson.)
              //* delete the images which are already submitted but removed from the measurement when inspection is reopen
              let whereCluse = `TAG1 = '${measurementItem.MEAS_ID}'`
              let inspAttachRes = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_ATTACHMENT,whereCluse);
              if(inspAttachRes.type==ResultType.success) {
                if(inspAttachRes.data.length>0) {
                  existingAttchments= inspAttachRes.data;
                  for(let m=0;m<existingAttchments.length;m++) {
                    if(fileNames.some(name => existingAttchments[m].FILE_NAME.includes(name))) {
                      if(existingAttchments[m].OBJECT_STATUS==0) {
                        // !below commented code is to resubmit the original image as well, if already submitted image is edited
                        // let foundMatchWithExistingEdited = false;
                        // if (measurementJson.originalImages && Array.isArray(measurementJson.originalImages.originalImgList)) {
                        //   measurementJson.originalImages.originalImgList.forEach((file) => {
                        //     let flName = file.originalImgList.changingThisBreaksApplicationSecurity.substring(
                        //       file.originalImgList.changingThisBreaksApplicationSecurity.lastIndexOf('/') + 1
                        //     );
                            
                        //     if (existingAttchments[m].FILE_NAME.includes(flName)) {
                        //       if ('existingEdited' in file && file.existingEdited === true) {
                        //         foundMatchWithExistingEdited = true;
                        //       }
                        //     }
                        //   });
                        // }
                        // // If no match found with existingEdited true, remove all filenames in existingAttchments[m].FILE_NAME
                        // if (!foundMatchWithExistingEdited) {
                        //   fileNames = fileNames.filter(name => !existingAttchments[m].FILE_NAME.includes(name));
                        // }

                        fileNames = fileNames.filter(name => !existingAttchments[m].FILE_NAME.includes(name));
                      } else {
                        if(!fileNames.includes(existingAttchments[m].FILE_NAME)) {
                          fileNames.push(existingAttchments[m].FILE_NAME);
                        }
                      }
                    } else {
                      if(existingAttchments[m].FILE_NAME.includes(existingAttchments[m].UID)) {
                        let file = existingAttchments[m].FILE_NAME;
                        let extractedFileName = file.replace(`${existingAttchments[m].UID}_`, '');
                        if(!fileNames.includes(extractedFileName)) {
                          fileNames.push(extractedFileName)
                        }
                      } else {
                        let extractedFileName = existingAttchments[m].FILE_NAME;
                        if(!fileNames.includes(extractedFileName)) {
                          fileNames.push(extractedFileName)
                        }
                      }
                      
                    }
                  }
                }
              } else {
                console.log("error reading insp attachment table:",inspAttachRes.error);
              }

              // !before deleting attachments
              let attRes:any = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_ATTACHMENT,`TAG1='${measurementItem.MEAS_ID}' AND FID = '${this.insertedHeader.LID}'`)
              if(attRes.type==ResultType.success) {
                console.log(attRes.data);
              }

              // Construct the IN clause part
              let inClause = fileNames.map(name => `'${name}'`).join(', ');
              // Construct the LIKE clauses
              let likeClauses = fileNames.map(name => `FILE_NAME LIKE '%${name}'`).join(' OR ');

              attRes='';
              attRes = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_ANNOTATION,`FILE_NAME IN (${inClause}) OR ${likeClauses}`)
              if(attRes.type==ResultType.success) {
                console.log(attRes.data);
              }

              // !include tiledImages files attachments also in reopened in case of external abrasion
              let attachRes = await this.unviredCordovaSDK.dbDelete(AppConstant.TABLE_INSPECTION_ATTACHMENT,`TAG1='${measurementItem.MEAS_ID}' AND FILE_NAME IN (${inClause}) OR ${likeClauses}`);
              if(attachRes.type==ResultType.success) {
                // await this.deleteAttachment()
                console.log("deleted the new images attachments in reopen inspection")
              } else {
                console.log("error deleting the new images attachments from inspection attachment table:",attachRes.error);
              }

              let annotRes = await this.unviredCordovaSDK.dbDelete(AppConstant.TABLE_ANNOTATION,`FILE_NAME IN (${inClause}) OR ${likeClauses}`);
              if(annotRes.type==ResultType.success) {
                // await this.deleteAttachment()
                console.log("deleted the new annotation in reopen inspection")
              } else {
                console.log("error deleteing annotation in reopen inspection",annotRes.error);
              }
              
              // !after removing attachments
              attRes='';
              attRes= await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_ATTACHMENT,`TAG1='${measurementItem.MEAS_ID}' AND FID = '${this.insertedHeader.LID}'`)
              if(attRes.type==ResultType.success) {
                console.log(attRes.data);
              }
              
              attRes='';
              attRes= await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_ANNOTATION,`MEAS_ID= '${measurementJson.id}' AND FID = '${this.insertedHeader.LID}'`)
              if(attRes.type==ResultType.success) {
                console.log(attRes.data);
              }

              measurementJson.id = measurementItem.MEAS_ID;
            } else {
              var deleteAttachment = await this.deleteAttachmentsForMeasurements(measurementItem.MEAS_ID)
              var deleteAnnotations = await this.deleteAnnotationsForMeasurements(measurementItem.MEAS_ID)
            }

            if (measurementJson.externalImage && measurementJson.externalImage.length > 0) {
              for (var i = 0; i < measurementJson.externalImage.length; i++) {
                var lat = '', long = ''
                if (measurementJson.originalImages && measurementJson.originalImages != undefined) {
                  if (measurementJson.originalImages.originalImgList.length > 0) {
                    if (measurementJson.originalImages.originalImgList[i] != undefined) {
                      if (measurementJson.originalImages.originalImgList[i].latitude != undefined) {
                        lat = measurementJson.originalImages.originalImgList[i].latitude
                      }
                    }
                  }
                }
                if (measurementJson.originalImages && measurementJson.originalImages != undefined) {
                  if (measurementJson.originalImages.originalImgList.length > 0) {
                    if (measurementJson.originalImages.originalImgList[i] != undefined) {
                      if (measurementJson.originalImages.originalImgList[i].longitude != undefined) {
                        long = measurementJson.originalImages.originalImgList[i].longitude
                      }
                    }
                  }
                }
                try {
                  let contents:any;
                  if(measurementJson.externalImage[i].mode == "insightAI")  {
                    let selectedRopeType=''
                    // let cert = this.visionService.selectedCertificate;
                    if(this.insertedHeader.PRODUCT?.includes('AMSTEEL') || this.insertedHeader.PRODUCT?.includes('ML-12')) {
                      selectedRopeType = 'amsteel'
                    } else if(this.insertedHeader.PRODUCT?.includes('TENEX')) {
                      selectedRopeType = 'tenex';
                    }
                    let modelVersion;
                    let response = await this.unviredCordovaSDK.dbSelect("AI_MODEL_VERSION_HEADER",`MODEL_TYPE='${selectedRopeType}'`)
                    if(response.type==ResultType.success) {
                      if(response.data && response.data.length>0) {
                        modelVersion = response.data[0].MODEL_NAME;
                      }
                    }
                    if(response.type==ResultType.error) {
                      modelVersion = '0.0.0'
                      this.unviredCordovaSDK.logInfo("VISIONAISERVICE", "createQuickInspectAttachments", " READ AI_MODEL_VERSION_HEADER " + JSON.stringify(response.error))
                    }

                    if(measurementJson.isFinalValuemanual && measurementJson.externalImage[i].tiledImage==undefined) {
                      measurementJson.originalImages.originalImgList[i].report = 'yes';
                    } else if(!measurementJson.isFinalValuemanual && measurementJson.externalImage[i].tiledImage==undefined) {
                      measurementJson.originalImages.originalImgList[i].report = 'no';
                    }

                    let bFileName:string;
                    let insightType:any = {"atttachType": "M", "insightImage":true, "insightAIRating": measurementJson.externalImage[i].insightAIScore,'modelVersion':modelVersion };
                    let currentdate = new Date();
                    let datetime = currentdate.getDate() + '' + (currentdate.getMonth() + 1) + '' +currentdate.getFullYear() + '' + currentdate.getHours() +'' + currentdate.getMinutes() + '' + currentdate.getSeconds()+''+currentdate.valueOf();
                    bFileName = measurementJson.id + datetime + "modified";

                    if(this.device.platform!='browser') {
                      let fileName = measurementJson.externalImage[i].Image.changingThisBreaksApplicationSecurity.substring(measurementJson.externalImage[i].Image.changingThisBreaksApplicationSecurity.lastIndexOf('/') + 1);
                      if(this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED && fileNames.includes(fileName) || this.insertedHeader.INSPECTION_STATUS!=AppConstant.REOPENED) {
                        contents = await this.createAttachmentItem(measurementJson, selectedInspHeader.data[0].LID, measurementJson.externalImage[i].Image.changingThisBreaksApplicationSecurity, i, "modified", measurementJson.originalImages.originalImgList[i].report, insightType, measurementJson.originalImages.originalImgList[i].latitude, measurementJson.originalImages.originalImgList[i].longitude)
                      }
                    } else {
                      let type = measurementJson.externalImage[i].Image.substring(measurementJson.externalImage[i].Image.indexOf("image/") + 6, measurementJson.externalImage[i].Image.indexOf(';')) 
                      let fileTitle = bFileName+ "." + type;
                      contents = await this.createAttachmentItem(measurementJson, selectedInspHeader.data[0].LID, measurementJson.externalImage[i].Image, i, "modified", measurementJson.originalImages.originalImgList[i].report, insightType, measurementJson.originalImages.originalImgList[i].latitude, measurementJson.originalImages.originalImgList[i].longitude,fileTitle)
                    }

                    if(measurementJson.externalImage[i].tiledImages && measurementJson.externalImage[i].tiledImages.length > 0) {
                      for(let j=0;j<measurementJson.externalImage[i].tiledImages.length;j++) {
                        let insightType:any = {"atttachType": "M","tiledImage":measurementJson.externalImage[i].tiledImages[j].tiledImage,tileScore:measurementJson.externalImage[i].tiledImages[j].tileScore,'modelVersion':modelVersion};
                        if(this.device.platform!='browser') {
                          let fileName = measurementJson.externalImage[i].tiledImages[j].Image.changingThisBreaksApplicationSecurity.substring(measurementJson.externalImage[i].tiledImages[j].Image.changingThisBreaksApplicationSecurity.lastIndexOf('/') + 1);
                          if(this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED && fileNames.includes(fileName) || this.insertedHeader.INSPECTION_STATUS!=AppConstant.REOPENED) {
                            contents = await this.createAttachmentItem(measurementJson, selectedInspHeader.data[0].LID, measurementJson.externalImage[i].tiledImages[j].Image.changingThisBreaksApplicationSecurity, j, "modified", "no", insightType, 0, 0)
                          } else {

                          }
                        } else {
                          let type = measurementJson.externalImage[i].tiledImages[j].Image.substring(measurementJson.externalImage[i].tiledImages[j].Image.indexOf("image/") + 6, measurementJson.externalImage[i].tiledImages[j].Image.indexOf(';')) 
                          let fileTitle = bFileName+'-T'+(j+1) + "." + type;
                          contents = await this.createAttachmentItem(measurementJson, selectedInspHeader.data[0].LID, measurementJson.externalImage[i].tiledImages[j].Image, j, "modified", "no", insightType, 0, 0,fileTitle)
                        }
                      }
                    }
                  } else {
                    var insightType:any = {"atttachType": "M", "insightImage":false, "insightAIRating": 0}
                    if(this.device.platform!='browser') {
                      let fileName = measurementJson.externalImage[i].Image.changingThisBreaksApplicationSecurity.substring(measurementJson.externalImage[i].Image.changingThisBreaksApplicationSecurity.lastIndexOf('/') + 1);
                      try {
                        if (this.insertedHeader.INSPECTION_STATUS == AppConstant.REOPENED && fileNames.includes(fileName) || this.insertedHeader.INSPECTION_STATUS != AppConstant.REOPENED) {
                          contents = await this.createAttachmentItem(measurementJson, selectedInspHeader.data[0].LID, measurementJson.externalImage[i].Image.changingThisBreaksApplicationSecurity, i, "modified", measurementJson.originalImages.originalImgList[i].report, insightType, measurementJson.originalImages.originalImgList[i].latitude, measurementJson.originalImages.originalImgList[i].longitude)
                        }
                      } catch (error) {
                        console.log("Error:" + JSON.stringify(error));
                        this.unviredCordovaSDK.logError("dataService", "saveMeasurements", " ATTACHMENT RESULT " + JSON.stringify(error))
                      }
                    } else {
                      let currentdate = new Date();
                      let datetime = currentdate.getDate() + '' + (currentdate.getMonth() + 1) + '' +currentdate.getFullYear() + '' + currentdate.getHours() +'' + currentdate.getMinutes() + '' + currentdate.getSeconds()+''+currentdate.valueOf();
                      let type = measurementJson.externalImage[i].Image.substring(measurementJson.externalImage[i].Image.indexOf("image/") + 6, measurementJson.externalImage[i].Image.indexOf(';'))
                      let bFileName = measurementJson.id + datetime + "original";
                      let fileTitle = bFileName+ "." + type;
                      contents = await this.createAttachmentItem(measurementJson, selectedInspHeader.data[0].LID, measurementJson.externalImage[i].Image, i, "modified", measurementJson.originalImages.originalImgList[i].report, insightType, measurementJson.originalImages.originalImgList[i].latitude, measurementJson.originalImages.originalImgList[i].longitude,fileTitle)
                    }
                    
                  }

                  this.unviredCordovaSDK.logInfo("dataService", "saveMeasurements", " ATTACHMENT RESULT " + JSON.stringify(contents))
                } catch (error) {
                  console.log("Error:"+JSON.stringify(error));
                  this.unviredCordovaSDK.logError("dataService", "saveMeasurements", " ATTACHMENT RESULT " + JSON.stringify(error))
                }
              }
            }
            if (measurementJson.originalImages && measurementJson.originalImages.originalImgList && measurementJson.originalImages.originalImgList.length > 0) {
              for (var i = 0; i < measurementJson.originalImages.originalImgList.length; i++) {
                try {
                  var insightType:any = {"atttachType": "M", "insightImage":false, "insightAIRating": 0}
                  let contents;
                  // let updateAnnotation = async (contents) =>{
                  //   this.unviredCordovaSDK.logInfo("dataService", "saveMeasurements", " ATTACHMENT RESULT " + JSON.stringify(contents))
                  //   var tempAnnotation = measurementJson.originalImages.originalImgList[i].annotationsForImage
                  //   if (tempAnnotation.length > 0 && tempAnnotation != "") {
                  //     var result = await this.insertOrUpdateAnnotation(tempAnnotation, contents.data[0].fields.UID, measurementJson, selectedInspHeader.data[0].LID, contents.data[0].fields.FILE_NAME)
                  //   }
                  // }
                  
                  if(this.device.platform!='browser') {
                    let fileName = measurementJson.originalImages.originalImgList[i].originalImgList.changingThisBreaksApplicationSecurity.substring(measurementJson.originalImages.originalImgList[i].originalImgList.changingThisBreaksApplicationSecurity.lastIndexOf('/') + 1);
                    if(this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED && fileNames.includes(fileName) || this.insertedHeader.INSPECTION_STATUS!=AppConstant.REOPENED) {
                      contents = await this.createAttachmentItem(measurementJson, selectedInspHeader.data[0].LID, measurementJson.originalImages.originalImgList[i].originalImgList.changingThisBreaksApplicationSecurity, i, "original", 'no', insightType, measurementJson.originalImages.originalImgList[i].latitude, measurementJson.originalImages.originalImgList[i].longitude)
                      // await updateAnnotation(contents);
                    } else if(this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED && existingAttchments.some(file=>file.FILE_NAME.includes(fileName)) && measurementJson.originalImages.originalImgList[i].annotationsForImage!="") {
                        let orgFile= existingAttchments.find(fl=>fl.FILE_NAME.includes(fileName));
                        let anRes = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_ANNOTATION,`ATTACH_ID='${orgFile.UID}' AND FID = '${selectedInspHeader.data[0].LID}'`);
                        if(anRes.type==ResultType.success) {
                          let annot;
                          if(anRes.data.length>0) {
                            annot = anRes.data[0];
                            if(annot.DATA!=measurementJson.originalImages.originalImgList[i].annotationsForImage) {
                              annot.DATA = measurementJson.originalImages.originalImgList[i].annotationsForImage;
                              annot.MEAS_ID = measurementJson.id;
                              annot.U_MODE ="U";
                              let updateRes = await this.unviredCordovaSDK.dbUpdate(AppConstant.TABLE_ANNOTATION,annot,`ATTACH_ID='${orgFile.UID}' AND FID = '${selectedInspHeader.data[0].LID}'`);
                              if(updateRes.type==ResultType.success) {
                                console.log("updated annotation");
                              }
                            }
                          } else {
                            //* create annotation for it
                            let tmpAnnotation =measurementJson.originalImages.originalImgList[i].annotationsForImage;
                            var result = await this.insertOrUpdateAnnotation(tmpAnnotation, orgFile.UID, measurementJson, selectedInspHeader.data[0].LID, fileName)
                          }
                        }
                    }
                  } else {
                    var currentdate = new Date();
                    var datetime = currentdate.getDate() + '' + (currentdate.getMonth() + 1) + '' +currentdate.getFullYear() + '' + currentdate.getHours() +'' + currentdate.getMinutes() + '' + currentdate.getSeconds()+''+currentdate.valueOf();
                    let bFileName = measurementJson.id + datetime + "original";
                    let type = measurementJson.originalImages.originalImgList[i].originalImgList.substring(measurementJson.originalImages.originalImgList[i].originalImgList.indexOf("image/") + 6, measurementJson.originalImages.originalImgList[i].originalImgList.indexOf(';'))
                    let fileTitle = bFileName+ "." + type;
                    contents = await this.createAttachmentItem(measurementJson, selectedInspHeader.data[0].LID, measurementJson.originalImages.originalImgList[i].originalImgList, i, "original", 'no', insightType, measurementJson.originalImages.originalImgList[i].latitude, measurementJson.originalImages.originalImgList[i].longitude,fileTitle)
                    // await updateAnnotation(contents);
                  }
                  this.unviredCordovaSDK.logInfo("dataService", "saveMeasurements", " ATTACHMENT RESULT " + JSON.stringify(contents))
                    var tempAnnotation = measurementJson.originalImages.originalImgList[i].annotationsForImage
                    if (tempAnnotation.length > 0 && tempAnnotation != "" && contents!=null && contents!=undefined && contents!="" && contents?.data!=undefined) {
                      var result = await this.insertOrUpdateAnnotation(tempAnnotation, contents.data[0].fields.UID, measurementJson, selectedInspHeader.data[0].LID, contents.data[0].fields.FILE_NAME)
                    }
                } catch (error) {
                  console.log(error);
                  this.unviredCordovaSDK.logError("dataService", "saveMeasurements", " ATTACHMENT RESULT " + JSON.stringify(error))
                }
              }
            }
          }
        } else {
          console.log("header not found")
        }
      }
    }
    if (noReset == true) {
      this.cameraService.reset();
    }
    return selectedInspHeader
  }

  async saveConfigMeasurements(measurementJson: any, configName: string, update?: boolean, noReset?: boolean, createAttachment?: boolean): Promise<any> {
    var temp = false, resetFlag = false;
    if (noReset == true) {
      resetFlag = true;
    }
    if (update == true) {
      temp = true;
    }
    if (createAttachment == undefined) {
      createAttachment = true;
    }
    var selectedInspHeader = await this.getSelectedHeaderFromDb(this.insertedHeader.INSPECTION_ID)
    if (selectedInspHeader.type == ResultType.success) {
      if (measurementJson.id != undefined && measurementJson.id != "") {
        console.log("selected item =============" + JSON.stringify(selectedInspHeader.data))
        if (selectedInspHeader.data.length > 0) {
          var jsonString = '';
          var measurementItem;
          jsonString = JSON.stringify(measurementJson);
          measurementItem = new CONFIGURATION();
          measurementItem.DATA = jsonString;
          measurementItem.INSPECTION_ID = this.insertedHeader.INSPECTION_ID;
          measurementItem.CONFIG_ID = measurementJson.id;
          measurementItem.CONFIG_TYPE_ID = measurementJson.type;
          measurementItem.FID = selectedInspHeader.data[0].LID
          measurementItem.CONFIG_NAME = configName;
          if(this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED) {
            measurementItem.U_MODE = 'A';
          }
          // if(update) {
            let measRes = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_CONFIGURATION_ITEM,`INSPECTION_ID = '${this.insertedHeader.INSPECTION_ID}' AND DATA LIKE '%"id":"${measurementJson.id}"%'`);
            if(measRes.type == ResultType.success) {
              if(measRes.data.length>0) {
                console.log("measurement Item 🙂🙂🙂🙂🙂:",measurementItem);
                measurementItem = measRes.data[0];
                measurementItem.DATA = jsonString;
                if(this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED && measurementItem.OBJECT_STATUS!=1 && measurementItem.OBJECT_STATUS!=2) {
                  measurementItem.OBJECT_STATUS = 2;
                  measurementItem.U_MODE = 'U'
                }
              }
            } else {
              console.log("configuration table data hoge hakskondide,data illa 😭😭😭😭");
            }
          // }

          // selectedHeader = await this.createAttachmentItem(measurementJson, selectedHeader.data[0].LID)     
          var selectedHeader = await this.unviredCordovaSDK.dbInsertOrUpdate(AppConstant.TABLE_CONFIGURATION_ITEM, measurementItem, false)
          if (createAttachment == true) {
            let filesToDelete =[];
            let existingAttchments =[];
            if(this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED) {
              if(measurementJson.externalImage.length>0) {
                for(let i=0;i<measurementJson.externalImage.length;i++) {
                  let fileName = measurementJson.externalImage[i].Image.changingThisBreaksApplicationSecurity.substring(measurementJson.externalImage[i].Image.changingThisBreaksApplicationSecurity.lastIndexOf('/') + 1);
                  filesToDelete.push(fileName);
                }
              }

              if(measurementJson.originalImages.originalImgList.length>0) {
                for(let i=0;i<measurementJson.originalImages.originalImgList.length;i++) {
                  let fileName = measurementJson.originalImages.originalImgList[i].originalImgList.changingThisBreaksApplicationSecurity.substring(measurementJson.originalImages.originalImgList[i].originalImgList.changingThisBreaksApplicationSecurity.lastIndexOf('/') + 1);
                  filesToDelete.push(fileName);
                }
              }
              

              //* delete the images which are already submitted but removed from the measurement when inspection is reopen
              let whereCluse = `TAG1 = '${measurementItem.CONFIG_ID}'`
              let inspAttachRes = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_ATTACHMENT,whereCluse);
              if(inspAttachRes.type==ResultType.success) {
                if(inspAttachRes.data.length>0) {
                  existingAttchments= inspAttachRes.data;
                  for(let m=0;m<existingAttchments.length;m++) {
                    if(filesToDelete.some(name => existingAttchments[m].FILE_NAME.includes(name))) {
                      if(existingAttchments[m].OBJECT_STATUS==0) {
                        filesToDelete = filesToDelete.filter(name => !existingAttchments[m].FILE_NAME.includes(name));
                      } else {
                        if(!filesToDelete.includes(existingAttchments[m].FILE_NAME)) {
                          filesToDelete.push(existingAttchments[m].FILE_NAME);
                        }
                      }
                    } else {
                      if(existingAttchments[m].FILE_NAME.includes(existingAttchments[m].UID)) {//! in android UID will be appended to fileName when attachments are synced, so check if UID is there in the FILE_NAME
                        let file = existingAttchments[m].FILE_NAME;
                        let extractedFileName = file.replace(`${existingAttchments[m].UID}_`, '');
                        if(!filesToDelete.includes(extractedFileName)) {
                          filesToDelete.push(extractedFileName)
                        }
                      } else { //! if UID is not there in FILE_NAME the pick the file name directly 
                        let file = existingAttchments[m].FILE_NAME;
                        if(!filesToDelete.includes(file)) {
                          filesToDelete.push(file)
                        }
                      }
                    }
                  }
                }
              } else {
                console.log("error reading insp attachment table:",inspAttachRes.error);
              }

              // !before deleting attachments
              let attRes:any = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_ATTACHMENT,`TAG1='${measurementItem.CONFIG_ID}' AND FID = '${this.insertedHeader.LID}'`)
              if(attRes.type==ResultType.success) {
                console.log(attRes.data);
              }

              // Construct the IN clause part
              let inClause = filesToDelete.map(name => `'${name}'`).join(', ');
              // Construct the LIKE clauses
              let likeClauses = filesToDelete.map(name => `FILE_NAME LIKE '%${name}'`).join(' OR ');

              // !include tiledImages files attachments also in reopened in case of external abrasion
              let attachRes = await this.unviredCordovaSDK.dbDelete(AppConstant.TABLE_INSPECTION_ATTACHMENT,`TAG1='${measurementItem.CONFIG_ID}' AND FILE_NAME IN (${inClause}) OR ${likeClauses}`);
              if(attachRes.type==ResultType.success) {
                // await this.deleteAttachment()
                console.log("deleted the new images attachments in reopen inspection")
              } else {
                console.log("error deleting the new images attachments from inspection attachment table:",attachRes.error);
              }
              
              // !after removing attachments
              attRes='';
              attRes= await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_ATTACHMENT,`TAG1='${measurementItem.CONFIG_ID}' AND FID = '${this.insertedHeader.LID}'`)
              if(attRes.type==ResultType.success) {
                console.log(attRes.data);
              }
              // let deleteAttach = await this.deleteAttachmentsForMeasurements(measurementItem.CONFIG_ID)
              // let deleteAnnotation = await this.deleteAnnotationsForMeasurements(measurementItem.CONFIG_ID)
              measurementJson.id = measurementItem.CONFIG_ID;
            } else {
              var deleteAttachment = await this.deleteAttachmentsForMeasurements(measurementItem.CONFIG_ID)
              var deleteAnnotations = await this.deleteAnnotationsForMeasurements(measurementItem.CONFIG_ID)
            }
            if (measurementJson.externalImage && measurementJson.externalImage.length > 0) {
              for (var i = 0; i < measurementJson.externalImage.length; i++) {
                var lat = '', long = ''
                if (measurementJson.originalImages != undefined) {
                  if (measurementJson.originalImages.originalImgList.length > 0) {
                    if (measurementJson.originalImages.originalImgList[i] != undefined) {
                      if (measurementJson.originalImages.originalImgList[i].latitude != undefined) {
                        lat = measurementJson.originalImages.originalImgList[i].latitude
                      }
                    }
                  }
                }
                if (measurementJson.originalImages != undefined) {
                  if (measurementJson.originalImages.originalImgList.length > 0) {
                    if (measurementJson.originalImages.originalImgList[i] != undefined) {
                      if (measurementJson.originalImages.originalImgList[i].longitude != undefined) {
                        long = measurementJson.originalImages.originalImgList[i].longitude
                      }
                    }
                  }
                }
                try {
                  if(measurementJson.externalImage[i].mode ==  "insightAI")  {
                    var insightType = {"atttachType": "C", "insightImage":true, "insightAIRating": 5};
                    measurementJson.originalImages.originalImgList[i]

                  } else {
                    var insightType = {"atttachType": "C", "insightImage":false, "insightAIRating": 0}
                  }
                  let contents;
                  let fileName = measurementJson.externalImage[i].Image.changingThisBreaksApplicationSecurity.substring(measurementJson.externalImage[i].Image.changingThisBreaksApplicationSecurity.lastIndexOf('/') + 1);
                  if((this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED && filesToDelete.includes(fileName)) || this.insertedHeader.INSPECTION_STATUS!=AppConstant.REOPENED) {
                    contents = await this.createAttachmentItem(measurementJson, selectedInspHeader.data[0].LID, measurementJson.externalImage[i].Image.changingThisBreaksApplicationSecurity, i, "modified", measurementJson.originalImages.originalImgList[i].report, insightType, measurementJson.originalImages.originalImgList[i].latitude, measurementJson.originalImages.originalImgList[i].longitude)
                  }
                  this.unviredCordovaSDK.logInfo("dataService", "saveMeasurements", " ATTACHMENT RESULT " + JSON.stringify(contents))
                } catch (error) {
                  this.unviredCordovaSDK.logError("dataService", "saveMeasurements", " ATTACHMENT RESULT " + JSON.stringify(error))
                }
              }
            }

            if (measurementJson.originalImages && measurementJson.originalImages.originalImgList && measurementJson.originalImages.originalImgList.length > 0) {
              for (var i = 0; i < measurementJson.originalImages.originalImgList.length; i++) {
                try {
                  var insightType = {"atttachType": "C", "insightImage":false, "insightAIRating": 0}
                  let contents;
                  let fileName = measurementJson.originalImages.originalImgList[i].originalImgList.changingThisBreaksApplicationSecurity.substring(measurementJson.originalImages.originalImgList[i].originalImgList.changingThisBreaksApplicationSecurity.lastIndexOf('/') + 1);
                  if((this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED && filesToDelete.includes(fileName)) || this.insertedHeader.INSPECTION_STATUS!=AppConstant.REOPENED) {
                    contents = await this.createAttachmentItem(measurementJson, selectedInspHeader.data[0].LID, measurementJson.originalImages.originalImgList[i].originalImgList.changingThisBreaksApplicationSecurity, i, "original", 'no', insightType, measurementJson.originalImages.originalImgList[i].latitude, measurementJson.originalImages.originalImgList[i].longitude)
                  }  else if(this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED && existingAttchments.some(file=>file.FILE_NAME.includes(fileName)) && measurementJson.originalImages.originalImgList[i].annotationsForImage!="") {
                    let orgFile= existingAttchments.find(fl=>fl.FILE_NAME.includes(fileName));
                    let anRes = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_ANNOTATION,`ATTACH_ID='${orgFile.UID}' AND FID = '${selectedInspHeader.data[0].LID}'`);
                    if(anRes.type==ResultType.success) {
                      let annot;
                      if(anRes.data.length>0) {
                        annot = anRes.data[0];
                        if(annot.DATA!=measurementJson.originalImages.originalImgList[i].annotationsForImage) {
                          annot.DATA = measurementJson.originalImages.originalImgList[i].annotationsForImage;
                          annot.MEAS_ID = measurementJson.id;
                          annot.U_MODE ="U";
                          let updateRes = await this.unviredCordovaSDK.dbUpdate(AppConstant.TABLE_ANNOTATION,annot,`ATTACH_ID='${orgFile.UID}' AND FID = '${selectedInspHeader.data[0].LID}'`);
                          if(updateRes.type==ResultType.success) {
                            console.log("updated annotation");
                          }
                        }
                      } else {
                        //* create annotation for it
                        let tmpAnnotation =measurementJson.originalImages.originalImgList[i].annotationsForImage;
                        var result = await this.insertOrUpdateAnnotation(tmpAnnotation, orgFile.UID, measurementJson, selectedInspHeader.data[0].LID, fileName)
                      }
                    }
                }
                  this.unviredCordovaSDK.logInfo("dataService", "saveMeasurements", " ATTACHMENT RESULT " + JSON.stringify(contents))
                  var tempAnnotation = measurementJson.originalImages.originalImgList[i].annotationsForImage
                  if (tempAnnotation.length > 0 && tempAnnotation != "" && contents!=null && contents!=undefined && contents!="" && contents?.data!=undefined) {
                    var result = await this.insertOrUpdateAnnotation(tempAnnotation, contents.data[0].fields.UID, measurementJson, selectedInspHeader.data[0].LID, contents.data[0].fields.FILE_NAME)
                  }
                } catch (error) {
                  this.unviredCordovaSDK.logError("dataService", "saveMeasurements", " ATTACHMENT RESULT " + JSON.stringify(error))
                }
              }
            }
          }
        } else {
          console.log("header not found")
        }
      }
    }
    if (noReset == true) {
      this.cameraService.reset();
    }
    return selectedInspHeader
  }

  async saveGuestMeasurements(measurementJson: any, update?: boolean, noReset?: boolean, createAttachment?: boolean): Promise<any> {
    var temp = false, resetFlag = false;
    if (noReset == true) {
      resetFlag = true;
    }
    if (update == true) {
      temp = true;
    }
    if (createAttachment == undefined) {
      createAttachment = true;
    }
    var selectedInspHeader = await this.getSelectedHeaderFromDb(this.insertedHeader.INSPECTION_ID)
    if (selectedInspHeader.type == ResultType.success) {
      console.log('------------- Present Alert ------------------');
      this.alertService.present()
      if (measurementJson.id != undefined && measurementJson.id != "") {
        console.log("selected item =============" + JSON.stringify(selectedInspHeader.data))
        if (selectedInspHeader.data.length > 0) {
          var jsonString = JSON.stringify(measurementJson);
          var measurementItem = new MEASUREMENT();
          measurementItem.DATA = jsonString;
          measurementItem.INSPECTION_ID = this.insertedHeader.INSPECTION_ID;
          measurementItem.MEAS_ID = measurementJson.id;
          measurementItem.MEAS_TYPE_ID = measurementJson.type;
          measurementItem.FID = selectedInspHeader.data[0].LID
          if (temp == false) {
            measurementItem.MEAS_TIMESTAMP = new Date().getTime();
          } else {
            var updatingMeas = await this.unviredCordovaSDK.dbSelect("MEASUREMENT", "MEAS_ID like '" + measurementJson.id + "'")
            if (updatingMeas.type == ResultType.success) {
              if (updatingMeas.data.length > 0) {
                measurementItem.MEAS_TIMESTAMP = updatingMeas.data[0].MEAS_TIMESTAMP;
              } else {
                measurementItem.MEAS_TIMESTAMP = new Date().getTime();
              }
            } else {
              measurementItem.MEAS_TIMESTAMP = new Date().getTime();
            }
          }
          // selectedHeader = await this.createAttachmentItem(measurementJson, selectedHeader.data[0].LID)     
          var selectedHeader = await this.unviredCordovaSDK.dbInsertOrUpdate(AppConstant.TABLE_MEASUREMENT_ITEM, measurementItem, false)
          if (createAttachment == true) {
            var deleteAttachment = await this.deleteAttachmentsForMeasurements(measurementJson.id)
            var deleteAnnotations = await this.deleteAnnotationsForMeasurements(measurementJson.id)
            if (measurementJson.externalImage && measurementJson.externalImage.length > 0) {
              for (var i = 0; i < measurementJson.externalImage.length; i++) {
                var lat = '', long = ''
                if (measurementJson.originalImages != undefined) {
                  if (measurementJson.originalImages.originalImgList.length > 0) {
                    if (measurementJson.originalImages.originalImgList[i] != undefined) {
                      if (measurementJson.originalImages.originalImgList[i].latitude != undefined) {
                        lat = measurementJson.originalImages.originalImgList[i].latitude
                      }
                    }
                  }
                }
                if (measurementJson.originalImages != undefined) {
                  if (measurementJson.originalImages.originalImgList.length > 0) {
                    if (measurementJson.originalImages.originalImgList[i] != undefined) {
                      if (measurementJson.originalImages.originalImgList[i].longitude != undefined) {
                        long = measurementJson.originalImages.originalImgList[i].longitude
                      }
                    }
                  }
                }
                if(measurementJson.externalImage[i].mode ==  "icaria Vision")  {
                  var insightType = {"atttachType": "M", "insightImage":true, "insightAIRating": 5}
                } else {
                  var insightType = {"atttachType": "M", "insightImage":false, "insightAIRating": 5}
                }
                const contents = await this.createAttachmentItem(measurementJson, selectedInspHeader.data[0].LID, measurementJson.externalImage[i].Image.changingThisBreaksApplicationSecurity, i, "modified", insightType, measurementJson.originalImages.originalImgList[i].report, measurementJson.originalImages.originalImgList[i].latitude, measurementJson.originalImages.originalImgList[i].longitude)
                console.log(" ATTACHMENT RESULT " + JSON.stringify(contents))
              }
            }
            if (measurementJson.originalImages && measurementJson.originalImages.originalImgList && measurementJson.originalImages.originalImgList.length > 0) {
              for (var i = 0; i < measurementJson.originalImages.originalImgList.length; i++) {
                var insightType = {"atttachType": "C", "insightImage":false, "insightAIRating": 0}
                const contents = await this.createAttachmentItem(measurementJson, selectedInspHeader.data[0].LID,measurementJson.originalImages.originalImgList[i].originalImgList.changingThisBreaksApplicationSecurity, i, "original", 'no', insightType,measurementJson.originalImages.originalImgList[i].latitude, measurementJson.originalImages.originalImgList[i].longitude)
                this.unviredCordovaSDK.logInfo("", "", " ATTACHMENT RESULT " + JSON.stringify(contents))
                var tempAnnotation = measurementJson.originalImages.originalImgList[i].annotationsForImage
                if (tempAnnotation.length > 0 && tempAnnotation != "") {
                  var result = await this.insertOrUpdateAnnotation(tempAnnotation, contents.data[0].fields.UID, measurementJson, selectedInspHeader.data[0].LID, contents.data[0].fields.FILE_NAME)
                }
              }
            }
          }
          console.log('Alert dismissed')
          this.alertService.dismiss()
          console.log("============================= dismiss alert =======================")
        } else {
          console.log("header not found")
          this.alertService.dismiss();
        }
      }
    }
    if (noReset == true) {
      this.cameraService.reset();
    }
    return selectedInspHeader
  }

  async deleteAttachmentsForMeasurements(measId) {
        this.unviredCordovaSDK.logInfo("data serveice", "deleteAttachmentForMeasurement", "measId " + measId)
    return await this.unviredCordovaSDK.dbExecuteStatement("Delete from INSPECTION_ATTACHMENT where TAG1 = '" + measId + "'")  
    // return await this.unviredCordovaSDK.dbDelete("INSPECTION_ATTACHMENT", "TAG1 like '" + measId + "'")
  }

  async deleteAnnotationsForMeasurements(measId) {
    return await this.unviredCordovaSDK.dbDelete("ANNOTATION", "MEAS_ID like '" + measId + "'")
  }

  async deleteAttachment(id:any){
    this.unviredCordovaSDK.logInfo("data serveice", "deleteAttachmentForMeasurement", "measId " + id)
    return await this.unviredCordovaSDK.dbExecuteStatement("Delete from INSPECTION_ATTACHMENT where TAG1 = '" + id + "'") 
  }

  async deleteAnnotation(id:any) {
    return await this.unviredCordovaSDK.dbDelete("ANNOTATION", "MEAS_ID like '" + id + "'")
  }

  async insertOrUpdateAnnotation(annotation: any, attachmentId?: any, mesID?: any, lid?: any, fileName?: any) {
    console.log(annotation)
    var annotationItem = new ANNOTATION();
    annotationItem.ANNOTATION_ID = UtilserviceService.guid();
    annotationItem.ATTACH_ID = attachmentId;
    annotationItem.DATA = annotation;
    annotationItem.MEAS_ID = mesID.id;
    annotationItem.FID = lid
    annotationItem.FILE_NAME = fileName
    annotationItem.INSPECTION_ID = this.insertedHeader.INSPECTION_ID;
    // annotationItem.U_MODE = 'A';
    if(this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED) {
      annotationItem.U_MODE = 'A';
    }

    let annotRes = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_ANNOTATION,`MEAS_ID ='${mesID.id}'`);
    if(annotRes.type == ResultType.success) {
      if(annotRes.data.length>0) {
        console.log("measurement Item 🙂🙂🙂🙂🙂:",annotationItem);
        annotationItem = annotRes.data[0];
        annotationItem.DATA = annotation;
        if(this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED && annotationItem.OBJECT_STATUS==0) {
          annotationItem.OBJECT_STATUS = 2;
          annotationItem.U_MODE = this.insertedHeader.INSPECTION_STATUS==AppConstant.REOPENED ? 'U' : '';
        }
      }
    } else {
      console.log("configuration table data hoge hakskondide,data illa 😭😭😭😭");
    }
    return this.unviredCordovaSDK.dbInsertOrUpdate("ANNOTATION", annotationItem, false)
  }

  async getSelectedHeaderFromDb(inspectionId) {
    var whereClause = "INSPECTION_ID like '" + inspectionId + "'"
    return await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_INSPECTION_HEADER, whereClause)
  }

  async getSelectedLMDHeaderFromDb(lmdId) {
    var whereClause = "LMD_ID like '" + lmdId + "'"
    return await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_LMD_HEADER, whereClause)
  }

  async createAttachmentItem(jsonString: any, lid: string, path: string, srNo: any, attachmenState: any, report: any, measurementType: any, lat?: any, long?: any,fileTitle?: string) {
    // this.file.readAsDataURL()
    var _self = this;
    var imageData = path        //jsonString.externalImage[0].changingThisBreaksApplicationSecurity
    var winImgLocalPath = '';
    if (this.platformId == 'electron' || _self.device.platform == 'browser') {
      winImgLocalPath = imageData;
    } else {
      // imageData = imageData.substr(imageData.indexOf('_app_file_') + 10, imageData.length - 1)
    if (_self.platform.is("android")) {
        imageData = "file://" + imageData;
      } else {
        // imageData = "file:///" + imageData;
      }
    }
    console.log('File name extraction, Substring');
    var fileName = '';
    if(_self.device.platform != 'browser') {
      fileName = imageData.substring(imageData.lastIndexOf('\\') + 1)
    }
    console.log('File name: ', fileName);
    var attachmentObject = new INSPECTION_ATTACHMENT()
    attachmentObject.FID = lid
    attachmentObject.UID = UtilserviceService.guid();
    attachmentObject.EXTERNAL_URL = "";
    attachmentObject.FILE_NAME = fileName;
    console.log('Local path extraction, Substring, Local path: ', winImgLocalPath);
    attachmentObject.LOCAL_PATH = imageData;
    attachmentObject.TAG1 = jsonString.id ; //measurement id
    attachmentObject.TAG2 = attachmenState //original or modified
    attachmentObject.TAG3 = JSON.stringify(measurementType)
    attachmentObject.TAG4 = report;
    this.fileOpener.open(imageData, attachmentObject.MIME_TYPE);
    if (lat && long) {
      attachmentObject.TAG5 = lat + ',' + long
    }
    // console.log("ATTACHMENT OBJECT" + JSON.stringify(attachmentObject))
    if (this.device.platform == 'browser') {
      if(attachmenState == 'modified') {
        attachmentObject.EXTERNAL_URL = winImgLocalPath;
      } else {
        attachmentObject.EXTERNAL_URL = jsonString.originalImages.originalImgList[srNo].originalImgList;
      }
    //   var type = attachmentObject.EXTERNAL_URL.substring(attachmentObject.EXTERNAL_URL.indexOf("image/") + 6, attachmentObject.EXTERNAL_URL.indexOf(';')) 
      // attachmentObject.FILE_NAME = bFileName + "." + type;
      // ! check the above later
      attachmentObject.FILE_NAME = fileTitle;
      attachmentObject.LOCAL_PATH = '';
      this.unviredCordovaSDK.dbInsertOrUpdate("INSPECTION_ATTACHMENT",attachmentObject,false)
      return {
        "data" : [{
          "fields": attachmentObject
        }]
      }
    } else {
      console.log("attachmentObject:" + JSON.stringify(attachmentObject));
      // this.file.readAsDataURL()
      return await _self.unviredCordovaSDK.createAttachmentItem("INSPECTION_ATTACHMENT", attachmentObject)
    }
  }

  // for creating lmd attatchments
  async createLMDAttachmentItem( lid: string, imageObj:any,fileTitle?:string,modelType?:string) {
    this.unviredCordovaSDK.logInfo("DATASERVICE", "CREATELMDSTTACHMENTS", "CREATELMDSTTACHMENTS METHOD START");
      let _self = this;
      let imageData = imageObj.Image;       //jsonString.externalImage[0].changingThisBreaksApplicationSecurity
      let winImgLocalPath = '';
      if ( _self.device.platform == 'browser') {
        winImgLocalPath = imageData;
      } else if(this.platformId == 'electron') { 
        winImgLocalPath = imageData.changingThisBreaksApplicationSecurity;
      } else {
        imageData = imageData.changingThisBreaksApplicationSecurity.substr(imageData.changingThisBreaksApplicationSecurity.indexOf('_app_file_') + 10, imageData.changingThisBreaksApplicationSecurity.length - 1);
        if (_self.platform.is("android")) {
          imageData = "file://" + imageData;
        } else {
          // imageData = "file:///" + imageData;
        }
      }
      console.log('File name extraction, Substring');
      let fileName = '';
      if(_self.device.platform != 'browser') {
        if(this.platformId == 'electron') {
          fileName = imageData.changingThisBreaksApplicationSecurity.substring(imageData.changingThisBreaksApplicationSecurity.lastIndexOf('/') + 1)
        } else {
          fileName = imageData.substring(imageData.lastIndexOf('/') + 1)
        }
      }
      
      const arr = { label: 1, value: "One" };
      let modelVersion;
      modelType = modelType.toLocaleLowerCase();
      let response = await this.unviredCordovaSDK.dbSelect("AI_MODEL_VERSION_HEADER",`MODEL_TYPE='${modelType}'`)
      if(response.type==ResultType.success) {
        if(response.data && response.data.length>0) {
          modelVersion = response.data[0].MODEL_NAME;
        }
      }
      if(response.type==ResultType.error) {
        modelVersion = '0.0.0'
        this.unviredCordovaSDK.logInfo("VISIONAISERVICE", "createQuickInspectAttachments", " READ AI_MODEL_VERSION_HEADER " + JSON.stringify(response.error))
      }

      // Using object destructuring with rest operator
      const { Image, mode , ...tileImageData } = imageObj;
      tileImageData['modelVersion'] = modelVersion;
      console.log('File name: ', fileName);
      let attachmentObject = new LMD_ATTACHMENT();
      attachmentObject.FID = lid
      attachmentObject.UID = UtilserviceService.guid();
      attachmentObject.EXTERNAL_URL = "";
      attachmentObject.FILE_NAME = fileName;
      // console.log('Local path extraction, Substring, Local path: ', winImgLocalPath);
      attachmentObject.LOCAL_PATH = imageData;
      attachmentObject.TAG1 = JSON.stringify(tileImageData); //tileImage data
      
      attachmentObject.TAG2 = "";
      if (this.device.platform == 'browser') {
        attachmentObject.EXTERNAL_URL = winImgLocalPath;
        attachmentObject.FILE_NAME = fileTitle;
        attachmentObject.LOCAL_PATH = '';
        await this.unviredCordovaSDK.dbInsertOrUpdate("LMD_ATTACHMENT",attachmentObject,false)
        return {
          "data" : [{
            "fields": attachmentObject
          }]
        }
      } else {
        this.unviredCordovaSDK.logInfo("DATASERVICE", "CREATELMDSTTACHMENTS", "CREATING ATTACHMENT ITEM");
        console.log("attachmentObject:" + attachmentObject);
        return await _self.unviredCordovaSDK.createAttachmentItem("LMD_ATTACHMENT", attachmentObject)
      }
  }

  async selectAllMeasurements(inspectionHeader) {
    // this.unviredCordovaSDK.dbExecuteStatement("SELECT * from MEASUREMENT  WHERE INSPECTION_ID like 'qweqw' AND MEAS_TYPE_ID like 'cuts' ORDER BY TIME_STAMP DESC LIMIT 1")
    return await this.unviredCordovaSDK.dbSelect("MEASUREMENT", "INSPECTION_ID like '" + inspectionHeader.INSPECTION_ID + "' ORDER BY MEAS_TIMESTAMP DESC")
  }

  async selectAllConfigMeasurements(inspectionHeader) {
    return await this.unviredCordovaSDK.dbSelect("CONFIGURATION", "INSPECTION_ID like '" + inspectionHeader.INSPECTION_ID + "'")
  }

  async completeInspection(inspectionHeader,status) {
    inspectionHeader.INSPECTION_STATUS = status;
    return await this.unviredCordovaSDK.dbUpdate("INSPECTION_HEADER", inspectionHeader, "INSPECTION_ID like '" + inspectionHeader.INSPECTION_ID + "'")
  }

  async completeInspectionGuest(inspectionHeader) {
    inspectionHeader.INSPECTION_STATUS = AppConstant.COMPLETED
    return await this.unviredCordovaSDK.dbUpdate("INSPECTION_HEADER", inspectionHeader, "INSPECTION_ID like '" + inspectionHeader.INSPECTION_ID + "'")
  }

  async syncInspection(inspectionHeader) {
    // inspectionHeader.INSPECTION_STATUS = AppConstant.COMPLETED
    // return await this.unviredCordovaSDK.dbUpdate("INSPECTION_HEADER", inspectionHeader, "INSPECTION_ID like '" + inspectionHeader.INSPECTION_ID + "'")
  }

  async getCountInspections(account?:any,asset?:any) {
    let query=''
    if(account && asset) {
      // !this is to count inspections based on account and asset for employee login, since employee can switch between multiple accounts and assets in same login
      query = `SELECT COUNT (*) AS COUNT from INSPECTION_HEADER where (INSPECTION_STATUS like '${AppConstant.IN_PROGRESS}' OR INSPECTION_STATUS like '${AppConstant.READY}' OR INSPECTION_STATUS like '${AppConstant.REOPENED}' ) AND (IS_DELETED IS NULL OR IS_DELETED = '') AND ACCOUNT_ID='${account.ID}'AND ASSET_ID='${asset.ID}'`
    } else {
      query = "SELECT COUNT (*) AS COUNT from INSPECTION_HEADER where (INSPECTION_STATUS like '" + AppConstant.IN_PROGRESS + "' OR INSPECTION_STATUS like '" + AppConstant.READY + "' OR INSPECTION_STATUS like '" + AppConstant.REOPENED +"' ) AND (IS_DELETED IS NULL OR IS_DELETED = '')"
    }
    return await this.unviredCordovaSDK.dbExecuteStatement(query)
  }

  async getCountRoutineInspections() {
    return await this.unviredCordovaSDK.dbExecuteStatement("SELECT COUNT (*) AS COUNT from LMD_HEADER where LMD_TYPE like 'RoutineInspection' AND LMD_STATUS like 'InProgress'")
  }

  async getMasterData() {
    return await this.unviredCordovaSDK.syncForeground(RequestType.PULL, "", "", "ROPE_INSPECTIONS_PA_GET_CUSTOMIZATION", true)
  }

  async setSelectedUom(uom) {
    switch (uom) {
      case 'Centimeter':
        this.selectedUom = 'cm';
        this.selectedUomString = "centimeter"
        this.selectedUomStringTwist = "Centimeter"
        break;
      case 'Meter':
        this.selectedUom = 'm';
        this.selectedUomString = "meter"
        this.selectedUomStringTwist = "Meter"
        break;
      case 'Feet':
        this.selectedUom = 'ft'
        this.selectedUomString = "feet"
        this.selectedUomStringTwist = "Feet"
        break;
      case 'Inch':
        this.selectedUom = 'in'
        this.selectedUomString = "inch"
        this.selectedUomStringTwist = "Inch"
        break;
      case 'Millimeter':
        this.selectedUom = 'mm'
        this.selectedUomString = "millimeter"
        this.selectedUomStringTwist = "Millimeter"
        break;
      default: this.selectedUom = 'm';
        this.selectedUomString = "meter"
        this.selectedUomStringTwist = "Meter"
    }
  }

  async setLMDSelectedUom() {
    var selectedUom = await this.userPreferenceService.getUserPreference('uom');
    if (selectedUom == 'metric') {
      this.selectedLMDUomString = 'meter';
    } else {
      this.selectedLMDUomString = 'inch';
    }
  }

  async setRoutineInspectionSelectedUom() {
    var selectedUom = await this.userPreferenceService.getUserPreference('uom');
    if (selectedUom == 'metric') {
      this.selectedRoutineInspectionlengthUomString = 'meter';
      this.selectedRoutineInspectionDiamUomString = 'mm';
    } else {
      this.selectedRoutineInspectionlengthUomString = 'feet';
      this.selectedRoutineInspectionDiamUomString = "in"
    }
  }




  // getMessages() {
  //   this.refreshTimeout = setInterval(() => {
  //     console.log("getmessage called")
  //     this.unviredCordovaSDK.getMessages();
  //     this.unviredCordovaSDK.logInfo("DataService", "getMessages", "Refresh Timeout called.")
  //   }, AppConstant.REFRESH_TIMEOUT);
  // }

  refreshData() {
    if (this.refreshDataTimeout != null) {
      clearInterval(this.refreshDataTimeout)
      this.refreshDataTimeout = null;
    }
    this.refreshDataTimeout = setInterval(() => {
      console.log("getmessage called")
      this.unviredCordovaSDK.getMessages();
      this.unviredCordovaSDK.logInfo("DataService", "getMessages", "Refresh Timeout called.")
    }, AppConstant.REFRESH_DATA_TIMEOUT);
    if (this.refreshDataTimeout != null && this.clearRefreshTimeout == null) {
      this.clearRefreshData();
    } else {
      if (this.clearRefreshTimeout != null) {
        clearTimeout(this.clearRefreshTimeout);
        this.clearRefreshTimeout = null;
        this.clearRefreshData()
      }
    }
  }

  refreshDataWindows() {
    if (this.w_refreshDataTimeout != null) {
      clearInterval(this.w_refreshDataTimeout)
      this.w_refreshDataTimeout = null;
    }
    this.w_refreshDataTimeout = setInterval(() => {
      console.log("getmessage called")
      this.unviredCordovaSDK.getMessages();
      this.unviredCordovaSDK.logInfo("DataService", "getMessages", "Refresh Timeout called.")
    }, AppConstant.REFRESH_DATA_TIMEOUT_WINDOWS);
  }

  // clearRefreshInterval() {
  //   clearInterval(this.refreshTimeout);
  //   this.refreshTimeout = null
  //   console.log("cleared")
  // }

  // async clearRefreshDownloadComplete() {
  //   if(this.refreshTimeout != null)
  //   var temp = await this.unviredCordovaSDK.dbSelect("DWNLD_TIME_HEADER","")
  //   if(temp.type == ResultType.success) {
  //     if(temp.data.length > 0) {
  //       setTimeout(() => {
  //         this.clearRefreshInterval()
  //         this.deleteDownloadTime();
  //       }, 120000)        
  //     }      
  //   }
  // }

  clearRefreshData() {
    this.clearRefreshTimeout = setTimeout(() => {
      this.clearDataRefresh();
    }, 1200000);
  }

  deleteDownloadTime() {
    var temp = this.unviredCordovaSDK.dbDelete("DWNLD_TIME_HEADER", "")
  }

  clearDataRefresh() {
    clearInterval(this.refreshDataTimeout);
    this.refreshDataTimeout = null
    console.log("cleared")
  }

  async clearOnCompleteRefreshInterval() {
    var res = await this.unviredCordovaSDK.dbSelect("DWNLD_TIME_HEADER", "")
    if (res.type == ResultType.success) {
      clearInterval(this.refreshTimeout);
      console.log("cleared")
    }
  }

  getCustomization() {
    this.firstLogIn = true;
    this.browserFirstLogIn = true;
  }

  isEmpty(json) {
    var temp = JSON.stringify(json)
    if (json) {
      if (temp == "" || temp == "{}") {
        return true
      } else {
        return false;
      }
    } else {
      return true
    }

  }

  setChafeInspectionHeader(inspectionHeader) {
    if (inspectionHeader) {
      var setChafe = false;
      // var configRes = await this.unviredCordovaSDK.dbExecuteStatement(`SELECT * FROM CONFIGURATION WHERE INSPECTION_ID like '${inspectionHeader.INSPECTION_ID}' AND  (CONFIG_TYPE_ID like 'Chafe' OR CONFIG_TYPE_ID like 'End')`)
      this.unviredCordovaSDK.dbExecuteStatement(`SELECT * FROM CONFIGURATION WHERE INSPECTION_ID like '${inspectionHeader.INSPECTION_ID}' AND  (CONFIG_TYPE_ID like 'Chafe' OR CONFIG_TYPE_ID like 'End')`).then((configRes) => {
        if (configRes.type == ResultType.success) {
          if (configRes.data.length > 0) {
            var tempres = configRes.data
            tempres.filter(t => t.DATA.type === 'Chafe');
            if (tempres.length > 0) {
              setChafe = true;
            } else {
              tempres = configRes.data
              tempres.filter(t => t.DATA.type === 'End');
              if (tempres.length > 0) {
                for (var index = 0; index < tempres.length; index++) {
                  var tempType = JSON.parse(tempres[index].DATA)
                  tempType.otherData.endChafe.toLowerCase() != 'none'
                  setChafe = true;
                  break;
                }
              }
            }
            if (setChafe == true) {
              // var inspHeader = await this.unviredCordovaSDK.dbSelect("INSPECTION_HEADER", `INSPECTION_ID like '${inspectionHeader.INSPECTION_ID}'`)
              this.unviredCordovaSDK.dbSelect("INSPECTION_HEADER", `INSPECTION_ID like '${inspectionHeader.INSPECTION_ID}'`).then((inspHeader) => {
                if (inspHeader.type == ResultType.success) {
                  if (inspHeader.data.length > 0) {
                    inspHeader.data[0].HAS_CHAFE = 1
                    // var updateRes = await this.unviredCordovaSDK.dbUpdate("INSPECTION_HEADER", inspHeader.data[0], `INSPECTION_ID like '${inspectionHeader.INSPECTION_ID}'`)
                    this.unviredCordovaSDK.dbUpdate("INSPECTION_HEADER", inspHeader.data[0], `INSPECTION_ID like '${inspectionHeader.INSPECTION_ID}'`).then((updateRes) => {
                      this.setLocationAndLayer(inspHeader.data[0])
                    }, error => {
                      console.log("(New Inspection: getInitialData ) error " + JSON.stringify(error))
                      this.setLocationAndLayer(inspHeader.data[0])
                    })
                  }
                } else {
                  this.unviredCordovaSDK.logError("DATA Service", "setChafeInspectionHeader", JSON.stringify(inspHeader))
                  return
                }
              }, error => {
                console.log("(New Inspection: getInitialData ) error " + JSON.stringify(error))
              })
            } else {
              return;
            }
          } else {
            this.unviredCordovaSDK.logError("DATA Service", "setChafeInspectionHeader", JSON.stringify(configRes))
            return
          }
        } else {
          this.unviredCordovaSDK.logError("DATA Service", "setChafeInspectionHeader", JSON.stringify(configRes))
          return
          // var setChafe = false;
          // var endType = await this.unviredCordovaSDK.dbSelect("CONFIGURATION",`INSPECTION_ID like '${inspectionHeader.INSPECTION_ID}' AND CONFIG_TYPE_ID like 'End'`)
          // if(endType.type == ResultType.success) {
          //   if(endType.data.length > 0) {
          //     for(var index = 0; index < endType.data.length; index++) {
          //       endType.data[index].DATA.otherData.endChafe == 'NONE'
          //       setChafe = true;
          //       break;
          //     }
          //     if(setChafe == true) {
          //       var inspHeader = await this.unviredCordovaSDK.dbSelect("INSPECTION_HEADER", `INSPECTION_ID like '${inspectionHeader.INSPECTION_ID}'`)
          //       if(inspHeader.type == ResultType.success) {
          //         if(inspHeader.data.length > 0) {
          //           inspHeader.data[0].HAS_CHAFE = 1
          //           var updateRes = await this.unviredCordovaSDK.dbUpdate("INSPECTION_HEADER", inspHeader, `INSPECTION_ID like '${inspectionHeader.INSPECTION_ID}'`)
          //         }
          //       } else {
          //         this.unviredCordovaSDK.logError("DATA Service", "setChafeInspectionHeader", JSON.stringify(inspHeader))
          //       }    
          //     }
          //   }
          // } else {
          //   this.unviredCordovaSDK.logError("DATA Service", "setChafeInspectionHeader", JSON.stringify(endType))
          // }  
        }
      }, error => {
        console.log("(New Inspection: getInitialData ) error " + JSON.stringify(error))
      })
    }

  }

  setLocationAndLayer(inspectionHeader) {
    if (inspectionHeader) {
      if(inspectionHeader.CONSTRUCTION == undefined || inspectionHeader.CONSTRUCTION == '' || inspectionHeader.CONSTRUCTION == null) {
        if ((!(inspectionHeader.PRODUCT.toLowerCase().indexOf(('Link-It').toLowerCase()) > -1) &&
        !(inspectionHeader.PRODUCT.toLowerCase().indexOf(('Link It').toLowerCase()) > -1) &&
        !(inspectionHeader.PRODUCT.toLowerCase().indexOf(('LinkIt').toLowerCase()) > -1))) {
          this.showDataMissingAlert('Construction')
          return;
        }
      }
      if(inspectionHeader.PRODUCT_TYPE == null || inspectionHeader.PRODUCT_TYPE == undefined || inspectionHeader.PRODUCT_TYPE == '' ) {
        inspectionHeader.PRODUCT_TYPE = ''
        // this.showDataMissingAlert('Product Type')
        // return;
      }
      if (inspectionHeader.PRODUCT_TYPE && inspectionHeader.PRODUCT_TYPE != '') {
        if ((inspectionHeader.PRODUCT_TYPE.toLowerCase().indexOf(('Conventional Fiber (Class I)').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_TYPE.toLowerCase().indexOf(('HMSF (Class II)').toLowerCase()) > -1)) {
          if (inspectionHeader.CONSTRUCTION && inspectionHeader.CONSTRUCTION != '') {
            if ((inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('3-Strand').toLowerCase()) > -1) || (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6-Strand').toLowerCase()) > -1) ||
              ((inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('8-Strand').toLowerCase()) > -1) && (inspectionHeader.CONSTRUCTION == '8-Strand')) || (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('12-Strand').toLowerCase()) > -1) ||
              (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('16-Strand').toLowerCase()) > -1) || (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Cover Only').toLowerCase()) > -1) ||
              (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Round Plait').toLowerCase()) > -1) || (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Solid Braid').toLowerCase()) > -1) ||
              (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('12-Strand Round Plait').toLowerCase()) > -1) || (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('12x3-Strand').toLowerCase()) > -1) || (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('8x3-Strand').toLowerCase()) > -1) ||
              (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6x36 IWRC EEIPS').toLowerCase()) > -1) || (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6x37 IWRC').toLowerCase()) > -1)) {
              if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Grommet').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Splice Centered Grommet').toLowerCase()) > -1)) {
                if (inspectionHeader.HAS_CHAFE == 1) {
                  this.locationOptions = ["Leg 1", "Leg 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  this.layerOptions = ["Rope", "Chafe"]
                  this.layerOptionsImage = [
                    { item: 'Rope', img: './assets/img/Layer/SingleBraidChafeRope.png' },
                    { item: 'Chafe', img: './assets/img/Layer/SingleBraidChafeChafe.png' }
                  ]
                  // this.layerOptions = ['Cover', 'Core', 'Chafe']
                } else if (inspectionHeader.HAS_CHAFE == 0) {
                  this.locationOptions = ["Leg 1", "Leg 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  this.layerOptions = ["Rope"]
                  this.layerOptionsImage = [
                    { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                  ]
                  // this.layerOptions = ['Cover', 'Core']
                } else {
                  this.locationOptions = ["Leg 1", "Leg 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  this.layerOptions = ["Rope"]
                  this.layerOptionsImage = [
                    { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                  ]
                  // this.layerOptions = ['Cover', 'Core']
                }
              } else if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multiloop assembly').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Strop').toLowerCase()) > -1)) {
                if (inspectionHeader.HAS_CHAFE == 1) {
                  this.locationOptions = ["Side 1", "Side 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  this.layerOptions = ["Rope", "Chafe"]
                  this.layerOptionsImage = [
                    { item: 'Rope', img: './assets/img/Layer/SingleBraidChafeRope.png' },
                    { item: 'Chafe', img: './assets/img/Layer/SingleBraidChafeChafe.png' }
                  ]
                  // this.layerOptions = ['Cover', 'Core', 'Chafe']
                } else if (inspectionHeader.HAS_CHAFE == 0) {
                  this.locationOptions = ["Side 1", "Side 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  this.layerOptions = ["Rope"]
                  this.layerOptionsImage = [
                    { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                  ]
                  // this.layerOptions = ['Cover', 'Core']
                } else {
                  this.locationOptions = ["Side 1", "Side 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  this.layerOptions = ["Rope"]
                  this.layerOptionsImage = [
                    { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                  ]
                  // this.layerOptions = ['Cover', 'Core']
                }
              } else if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
                (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
                if (inspectionHeader.HAS_CHAFE == 1) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ['Cover', 'Core', 'Chafe']
                  this.layerOptions = ["Rope", "Chafe"]
                  this.layerOptionsImage = [
                    { item: 'Rope', img: './assets/img/Layer/SingleBraidChafeRope.png' },
                    { item: 'Chafe', img: './assets/img/Layer/SingleBraidChafeChafe.png' }
                  ]
                } else if (inspectionHeader.HAS_CHAFE == 0) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ['Cover', 'Core']
                  this.layerOptions = ["Rope"]
                  this.layerOptionsImage = [
                    { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                  ]
                } else {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ['Cover', 'Core']
                  this.layerOptions = ["Rope"]
                  this.layerOptionsImage = [
                    { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                  ]
                }
              } else if ((inspectionHeader.PRODUCT_CONFIG == 'TBD') || (inspectionHeader.PRODUCT_CONFIG == 'Other') || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Soft Shackle').toLowerCase()) > -1) ||
                (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multi-part Rope Sling').toLowerCase()) > -1)) {
                if (inspectionHeader.HAS_CHAFE == 1) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  this.layerOptions = ["Rope", "Chafe"]
                  this.layerOptionsImage = [
                    { item: 'Rope', img: './assets/img/Layer/SingleBraidChafeRope.png' },
                    { item: 'Chafe', img: './assets/img/Layer/SingleBraidChafeChafe.png' }
                  ]
                  // this.layerOptions = ['Cover', 'Core', 'Chafe']
                } else if (inspectionHeader.HAS_CHAFE == 0) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ['Cover', 'Core']
                  this.layerOptions = ["Rope"]
                  this.layerOptionsImage = [
                    { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                  ]
                } else {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ['Cover', 'Core']
                  this.layerOptions = ["Rope"]
                  this.layerOptionsImage = [
                    { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                  ]
                }
              } else {
                if (inspectionHeader.HAS_CHAFE == 1) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  this.layerOptions = ["Rope", "Chafe"]
                  this.layerOptionsImage = [
                    { item: 'Rope', img: './assets/img/Layer/SingleBraidChafeRope.png' },
                    { item: 'Chafe', img: './assets/img/Layer/SingleBraidChafeChafe.png' }
                  ]
                  // this.layerOptions = ['Cover', 'Core', 'Chafe']
                } else if (inspectionHeader.HAS_CHAFE == 0) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ['Cover', 'Core']
                  this.layerOptions = ["Rope"]
                  this.layerOptionsImage = [
                    { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                  ]
                } else {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ['Cover', 'Core']
                  this.layerOptions = ["Rope"]
                  this.layerOptionsImage = [
                    { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                  ]
                }
              }
            } else if ((inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('8-Strand Jacketed').toLowerCase()) > -1) ||
              (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed').toLowerCase()) > -1) ||
              (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Other').toLowerCase()) > -1) ||
              (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed / Multi-Core').toLowerCase()) > -1) ||
              (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed / Parallel Fiber Core').toLowerCase()) > -1) ||
              (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Cork Line').toLowerCase()) > -1) ||
              (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('HMSF').toLowerCase()) > -1) ||
              (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Lead Line').toLowerCase()) > -1) ||
              (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Core Dependent Double Braid').toLowerCase()) > -1)) {
              if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Grommet').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Splice Centered Grommet').toLowerCase()) > -1)) {
                if (inspectionHeader.HAS_CHAFE == 1) {
                  this.locationOptions = ["Leg 1", "Leg 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                  this.layerOptions = ["Cover", "Core", "Chafe"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                    { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' },
                    { item: 'Chafe', img: '' }
                  ]
                } else if (inspectionHeader.HAS_CHAFE == 0) {
                  this.locationOptions = ["Leg 1", "Leg 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                    { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                  ]
                } else {
                  this.locationOptions = ["Leg 1", "Leg 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                    { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                  ]
                }

              } else if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multiloop assembly').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Strop').toLowerCase()) > -1)) {
                if (inspectionHeader.HAS_CHAFE == 1) {
                  this.locationOptions = ["Side 1", "Side 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                  this.layerOptions = ["Cover", "Core", "Chafe"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                    { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' },
                    { item: 'Chafe', img: '' }
                  ]
                } else if (inspectionHeader.HAS_CHAFE == 0) {
                  this.locationOptions = ["Side 1", "Side 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                    { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                  ]
                } else {
                  this.locationOptions = ["Side 1", "Side 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                    { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                  ]
                }
              } else if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
                (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
                if (inspectionHeader.HAS_CHAFE == 1) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                  this.layerOptions = ["Cover", "Core", "Chafe"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                    { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' },
                    { item: 'Chafe', img: '' }
                  ]
                } else if (inspectionHeader.HAS_CHAFE == 0) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                    { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' },
                  ]
                } else {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                    { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                  ]
                }
              } else if ((inspectionHeader.PRODUCT_CONFIG == 'TBD') || (inspectionHeader.PRODUCT_CONFIG == 'Other') || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Soft Shackle').toLowerCase()) > -1) ||
                (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multi-part Rope Sling').toLowerCase()) > -1)) {
                if (inspectionHeader.HAS_CHAFE == 1) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                  this.layerOptions = ["Cover", "Core", "Chafe"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                    { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' },
                    { item: 'Chafe', img: '' }
                  ]
                } else if (inspectionHeader.HAS_CHAFE == 0) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                    { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                  ]
                } else {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                    { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                  ]
                }
              } else {
                if (inspectionHeader.HAS_CHAFE == 1) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  this.layerOptions = ["Cover", "Core", "Chafe"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                    { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' },
                    { item: 'Chafe', img: '' }
                  ]
                } else if (inspectionHeader.HAS_CHAFE == 0) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                    { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                  ]
                } else {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                    { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                  ]
                }
              }
            } else if (
              (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid / Multi-core').toLowerCase()) > -1) ||
              (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid / Parallel Fiber core').toLowerCase()) > -1) ||
              (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid').toLowerCase()) > -1)) {
              if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Grommet').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Splice Centered Grommet').toLowerCase()) > -1)) {
                if (inspectionHeader.HAS_CHAFE == 1) {
                  this.locationOptions = ["Leg 1", "Leg 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                  this.layerOptions = ["Cover", "Core", "Chafe"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/DB_Chafe_Core.png' },
                    { item: 'Cover', img: './assets/img/Layer/DB_Chafe_Cover.png' },
                    { item: 'Chafe', img: './assets/img/Layer/DB_Chafe_Chafe.png' }
                  ]
                } else if (inspectionHeader.HAS_CHAFE == 0) {
                  this.locationOptions = ["Leg 1", "Leg 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                    { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' },
                  ]
                } else {
                  this.locationOptions = ["Leg 1", "Leg 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                    { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                  ]
                }

              } else if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multiloop assembly').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Strop').toLowerCase()) > -1)) {
                if (inspectionHeader.HAS_CHAFE == 1) {
                  this.locationOptions = ["Side 1", "Side 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                  this.layerOptions = ["Cover", "Core", "Chafe"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/DB_Chafe_Core.png' },
                    { item: 'Cover', img: './assets/img/Layer/DB_Chafe_Cover.png' },
                    { item: 'Chafe', img: './assets/img/Layer/DB_Chafe_Chafe.png' }
                  ]
                } else if (inspectionHeader.HAS_CHAFE == 0) {
                  this.locationOptions = ["Side 1", "Side 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                    { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                  ]
                } else {
                  this.locationOptions = ["Side 1", "Side 2"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                    { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                    { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                  ]
                }
              } else if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
                (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
                if (inspectionHeader.HAS_CHAFE == 1) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                  this.layerOptions = ["Cover", "Core", "Chafe"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/DB_Chafe_Core.png' },
                    { item: 'Cover', img: './assets/img/Layer/DB_Chafe_Cover.png' },
                    { item: 'Chafe', img: './assets/img/Layer/DB_Chafe_Chafe.png' }
                  ]
                } else if (inspectionHeader.HAS_CHAFE == 0) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                    { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                  ]
                } else {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                    { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                  ]
                }
              } else if ((inspectionHeader.PRODUCT_CONFIG == 'TBD') || (inspectionHeader.PRODUCT_CONFIG == 'Other') || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Soft Shackle').toLowerCase()) > -1) ||
                (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multi-part Rope Sling').toLowerCase()) > -1)) {
                if (inspectionHeader.HAS_CHAFE == 1) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                  this.layerOptions = ["Cover", "Core", "Chafe"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/DB_Chafe_Core.png' },
                    { item: 'Cover', img: './assets/img/Layer/DB_Chafe_Cover.png' },
                    { item: 'Chafe', img: './assets/img/Layer/DB_Chafe_Chafe.png' }
                  ]
                } else if (inspectionHeader.HAS_CHAFE == 0) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                    { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                  ]
                } else {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  // this.layerOptions = ["Rope Cover", "Rope Core"]
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                    { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                  ]
                }
              } else {
                if (inspectionHeader.HAS_CHAFE == 1) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  this.layerOptions = ["Cover", "Core", "Chafe"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/DB_Chafe_Core.png' },
                    { item: 'Cover', img: './assets/img/Layer/DB_Chafe_Cover.png' },
                    { item: 'Chafe', img: './assets/img/Layer/DB_Chafe_Chafe.png' }
                  ]
                } else if (inspectionHeader.HAS_CHAFE == 0) {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                    { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                  ]
                } else {
                  this.locationOptions = ["Leg 1"]
                  this.legOptionsList = [
                    { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                  ];
                  this.layerOptions = ["Cover", "Core"]
                  this.layerOptionsImage = [
                    { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                    { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                  ]
                }
              }
            } else {
              this.locationOptions = ["Leg 1"]
              this.legOptionsList = [
                { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
              ];
              this.layerOptionsImage = [
                { item: 'Core', img: './assets/img/Layer/DB_Core.png' }
              ]
            }
          } else {
            this.locationOptions = ["Leg 1"]
            this.legOptionsList = [
              { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
            ];
            this.layerOptionsImage = [
              { item: 'Core', img: './assets/img/Layer/DB_Core.png' }
            ]
          }
        } else if ((inspectionHeader.PRODUCT_TYPE.toLowerCase().indexOf(('Steel').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_TYPE.toLowerCase().indexOf(('Hybrid SWR/Synthetic').toLowerCase()) > -1) ||
        (inspectionHeader.PRODUCT_TYPE.toLowerCase().indexOf(('Steel Wire and Hybrid SWR/Synthetic').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_TYPE.toLowerCase().indexOf(('Wire').toLowerCase()) > -1) ||
        (inspectionHeader.PRODUCT_TYPE.toLowerCase().indexOf(('Steel Wire').toLowerCase()) > -1)) {
          if ((inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('8-Strand Jacketed').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Other').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed / Multi-Core').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed / Parallel Fiber Core').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Cork Line').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('HMSF').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Lead Line').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Core Dependent Double Braid').toLowerCase()) > -1)) {
            if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
              (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
              if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Rope"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/CoreDepDBCore.png' }
                ]
              } else {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Rope"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/CoreDepDBCore.png' }
                ]
              }
            } else {
              this.locationOptions = ["Leg 1"]
              this.legOptionsList = [
                { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
              ];
              this.layerOptions = ["Rope"]
              this.layerOptionsImage = [
                { item: 'Rope', img: './assets/img/Layer/CoreDepDBCore.png' }
              ]
            }
          } else if ((inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid / Multi-core').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid / Parallel Fiber core').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6X36 IWRC EEIPS').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6X36 IWRC').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6X36SES(37) + IWRC').toLowerCase()) > -1)) {
            if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
              (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
              if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Rope"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/DB_Core.png' }
                ]
              } else {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Rope"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/DB_Core.png' }
                ]
              }
            } else {
              this.locationOptions = ["Leg 1"]
              this.legOptionsList = [
                { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
              ];
              this.layerOptions = ["Rope"]
              this.layerOptionsImage = [
                { item: 'Rope', img: './assets/img/Layer/DB_Core.png' }
              ]
            }
          } else {
            this.locationOptions = ["Leg 1"]
            this.legOptionsList = [
              { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
            ];
            this.layerOptions = ["Rope"]
            this.layerOptionsImage = [
              { item: 'Rope', img: './assets/img/Layer/DB_Core.png' }
            ]
          }
        } else {
          this.locationOptions = ["Leg 1"]
          this.legOptionsList = [
            { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
          ];
          this.layerOptions = ["Rope"]
          this.layerOptionsImage = [
            { item: 'Rope', img: './assets/img/Layer/DB_Core.png' }
          ]
        }
      } else {
        if (inspectionHeader.CONSTRUCTION && inspectionHeader.CONSTRUCTION != '') {
          if ((inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('3-Strand').toLowerCase()) > -1) || (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6-Strand').toLowerCase()) > -1) ||
            ((inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('8-Strand').toLowerCase()) > -1) && (inspectionHeader.CONSTRUCTION == '8-Strand')) || (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('12-Strand').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('16-Strand').toLowerCase()) > -1) || (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Cover Only').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Round Plait').toLowerCase()) > -1) || (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Solid Braid').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('12-Strand Round Plait').toLowerCase()) > -1) || (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('12x3-Strand').toLowerCase()) > -1) || (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('8x3-Strand').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6x36 IWRC EEIPS').toLowerCase()) > -1) || (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('6x37 IWRC').toLowerCase()) > -1)) {
            if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Grommet').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Splice Centered Grommet').toLowerCase()) > -1)) {
              if (inspectionHeader.HAS_CHAFE == 1) {
                this.locationOptions = ["Leg 1", "Leg 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                this.layerOptions = ["Rope", "Chafe"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/SingleBraidChafeRope.png' },
                  { item: 'Chafe', img: './assets/img/Layer/SingleBraidChafeChafe.png' }
                ]
              } else if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Leg 1", "Leg 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                this.layerOptions = ["Rope"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                ]
              } else {
                this.locationOptions = ["Leg 1", "Leg 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                this.layerOptions = ["Rope"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                ]
              }
            } else if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multiloop assembly').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Strop').toLowerCase()) > -1)) {
              if (inspectionHeader.HAS_CHAFE == 1) {
                this.locationOptions = ["Side 1", "Side 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                this.layerOptions = ["Rope", "Chafe"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/SingleBraidChafeRope.png' },
                  { item: 'Chafe', img: './assets/img/Layer/SingleBraidChafeChafe.png' }
                ]
              } else if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Side 1", "Side 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                this.layerOptions = ["Rope"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                ]
              } else {
                this.locationOptions = ["Side 1", "Side 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                this.layerOptions = ["Rope"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                ]
              }
            } else if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
              (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
              if (inspectionHeader.HAS_CHAFE == 1) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Rope", "Chafe"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/SingleBraidChafeRope.png' },
                  { item: 'Chafe', img: './assets/img/Layer/SingleBraidChafeChafe.png' }
                ]
              } else if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Rope"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                ]
              } else {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Rope"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                ]
              }
            } else if ((inspectionHeader.PRODUCT_CONFIG == 'TBD') || (inspectionHeader.PRODUCT_CONFIG == 'Other') || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Soft Shackle').toLowerCase()) > -1) ||
              (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multi-part Rope Sling').toLowerCase()) > -1)) {
              if (inspectionHeader.HAS_CHAFE == 1) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Rope", "Chafe"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/SingleBraidChafeRope.png' },
                  { item: 'Chafe', img: './assets/img/Layer/SingleBraidChafeChafe.png' }
                ]
              } else if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Rope"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                ]
              } else {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Rope"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                ]
              }
            } else {
              if (inspectionHeader.HAS_CHAFE == 1) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Rope", "Chafe"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/SingleBraidChafeRope.png' },
                  { item: 'Chafe', img: './assets/img/Layer/SingleBraidChafeChafe.png' }
                ]
              } else if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Rope"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                ]
              } else {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Rope"]
                this.layerOptionsImage = [
                  { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
                ]
              }
            }
          } else if ((inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('8-Strand Jacketed').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Other').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed / Multi-Core').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Jacketed / Parallel Fiber Core').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Cork Line').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('HMSF').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Lead Line').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Core Dependent Double Braid').toLowerCase()) > -1)) {
            if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Grommet').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Splice Centered Grommet').toLowerCase()) > -1)) {
              if (inspectionHeader.HAS_CHAFE == 1) {
                this.locationOptions = ["Leg 1", "Leg 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                this.layerOptions = ["Cover", "Core", "Chafe"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                  { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' },
                  { item: 'Chafe', img: '' }
                ]
              } else if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Leg 1", "Leg 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                  { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                ]
              } else {
                this.locationOptions = ["Leg 1", "Leg 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                  { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                ]
              }

            } else if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multiloop assembly').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Strop').toLowerCase()) > -1)) {
              if (inspectionHeader.HAS_CHAFE == 1) {
                this.locationOptions = ["Side 1", "Side 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                this.layerOptions = ["Cover", "Core", "Chafe"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                  { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' },
                  { item: 'Chafe', img: '' }
                ]
              } else if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Side 1", "Side 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                  { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                ]
              } else {
                this.locationOptions = ["Side 1", "Side 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                  { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                ]
              }
            } else if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
              (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
              if (inspectionHeader.HAS_CHAFE == 1) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                this.layerOptions = ["Cover", "Core", "Chafe"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                  { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' },
                  { item: 'Chafe', img: '' }
                ]
              } else if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                  { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                ]
              } else {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                  { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                ]
              }
            } else if ((inspectionHeader.PRODUCT_CONFIG == 'TBD') || (inspectionHeader.PRODUCT_CONFIG == 'Other') || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Soft Shackle').toLowerCase()) > -1) ||
              (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multi-part Rope Sling').toLowerCase()) > -1)) {
              if (inspectionHeader.HAS_CHAFE == 1) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                this.layerOptions = ["Cover", "Core", "Chafe"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                  { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' },
                  { item: 'Chafe', img: '' }
                ]
              } else if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                  { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                ]
              } else {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                  { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                ]
              }
            } else {
              if (inspectionHeader.HAS_CHAFE == 1) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Cover", "Core", "Chafe"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                  { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' },
                  { item: 'Chafe', img: '' }
                ]
              } else if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                  { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                ]
              } else {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/CoreDepDBCover.png' },
                  { item: 'Cover', img: './assets/img/Layer/CoreDepDBCore.png' }
                ]
              }
            }
          } else if ((inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid / Multi-core').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid / Parallel Fiber core').toLowerCase()) > -1) ||
            (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid').toLowerCase()) > -1)) {
            if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Grommet').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Splice Centered Grommet').toLowerCase()) > -1)) {
              if (inspectionHeader.HAS_CHAFE == 1) {
                this.locationOptions = ["Leg 1", "Leg 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                this.layerOptions = ["Cover", "Core", "Chafe"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/DB_Chafe_Core.png' },
                  { item: 'Cover', img: './assets/img/Layer/DB_Chafe_Cover.png' },
                  { item: 'Chafe', img: './assets/img/Layer/DB_Chafe_Chafe.png' }
                ]
              } else if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Leg 1", "Leg 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                  { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                ]
              } else {
                this.locationOptions = ["Leg 1", "Leg 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                  { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                ]
              }

            } else if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multiloop assembly').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Strop').toLowerCase()) > -1)) {
              if (inspectionHeader.HAS_CHAFE == 1) {
                this.locationOptions = ["Side 1", "Side 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                this.layerOptions = ["Cover", "Core", "Chafe"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/DB_Chafe_Core.png' },
                  { item: 'Cover', img: './assets/img/Layer/DB_Chafe_Cover.png' },
                  { item: 'Chafe', img: './assets/img/Layer/DB_Chafe_Chafe.png' }
                ]
              } else if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Side 1", "Side 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                  { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                ]
              } else {
                this.locationOptions = ["Side 1", "Side 2"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
                  { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                  { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                ]
              }
            } else if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
              (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
              if (inspectionHeader.HAS_CHAFE == 1) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                this.layerOptions = ["Cover", "Core", "Chafe"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/DB_Chafe_Core.png' },
                  { item: 'Cover', img: './assets/img/Layer/DB_Chafe_Cover.png' },
                  { item: 'Chafe', img: './assets/img/Layer/DB_Chafe_Chafe.png' }
                ]
              } else if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                  { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                ]
              } else {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                  { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                ]
              }
            } else if ((inspectionHeader.PRODUCT_CONFIG == 'TBD') || (inspectionHeader.PRODUCT_CONFIG == 'Other') || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Soft Shackle').toLowerCase()) > -1) ||
              (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multi-part Rope Sling').toLowerCase()) > -1)) {
              if (inspectionHeader.HAS_CHAFE == 1) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core", "Chafe"]
                this.layerOptions = ["Cover", "Core", "Chafe"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/DB_Chafe_Core.png' },
                  { item: 'Cover', img: './assets/img/Layer/DB_Chafe_Cover.png' },
                  { item: 'Chafe', img: './assets/img/Layer/DB_Chafe_Chafe.png' }
                ]
              } else if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                  { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                ]
              } else {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                // this.layerOptions = ["Rope Cover", "Rope Core"]
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                  { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                ]
              }
            } else {
              if (inspectionHeader.HAS_CHAFE == 1) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Cover", "Core", "Chafe"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/DB_Chafe_Core.png' },
                  { item: 'Cover', img: './assets/img/Layer/DB_Chafe_Cover.png' },
                  { item: 'Chafe', img: './assets/img/Layer/DB_Chafe_Chafe.png' }
                ]
              } else if (inspectionHeader.HAS_CHAFE == 0) {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                  { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                ]
              } else {
                this.locationOptions = ["Leg 1"]
                this.legOptionsList = [
                  { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
                ];
                this.layerOptions = ["Cover", "Core"]
                this.layerOptionsImage = [
                  { item: 'Core', img: './assets/img/Layer/DB_Core.png' },
                  { item: 'Cover', img: './assets/img/Layer/DB_Cover.png' }
                ]
              }
            }
          } else {
            this.locationOptions = ["Leg 1"]
            this.legOptionsList = [
              { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
            ];
            this.layerOptions = ["Rope"]
            this.layerOptionsImage = [
              { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
            ]
          }
        } else {
          this.locationOptions = ["Leg 1"]
          this.legOptionsList = [
            { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
          ];
          this.layerOptions = ["Rope"]
          this.layerOptionsImage = [
            { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
          ]
        }
      }
    } else {
      this.locationOptions = ["Leg 1"]
      this.legOptionsList = [
        { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
      ];
      this.layerOptions = ["Rope"]
      this.layerOptionsImage = [
        { item: 'Rope', img: './assets/img/Layer/SingleBraidRope.png' }
      ]
    }
  }

  getLocationOptions() {
    // return ["Leg 1", "Leg 2"]
    return this.locationOptions;
  }

  getLegOptions() {
    // return ["Leg 1", "Leg 2"]
    return this.legOptionsList;
  }

  getLayerOptionsGuest() {
    // return ["Rope Cover", "Rope Core", "Chafe"]
    return this.layerOptions;
  }

  // getLayerOptions() {
  //   // return ["Rope Cover", "Rope Core", "Chafe"]
  //   return this.layerOptions;
  // }

  getLayerOptions() {
    // return ["Rope Cover", "Rope Core", "Chafe"]
    return this.layerOptionsImage;
  }

  setConfigLocationOptions(inspectionHeader) {
    if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Grommet').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Splice Centered Grommet').toLowerCase()) > -1)) {
      this.configLocationOptions = ["Leg 1", "Leg 2"]
      this.configlegOptionsList = [
        { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
        { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
      ];
    } else if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Multiloop assembly').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Strop').toLowerCase()) > -1)) {
      this.configLocationOptions = ["Leg 1", "Leg 2"]
      this.configlegOptionsList = [
        { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' },
        { item: 'Grommet Leg 2', img: './assets/img/GrommetLeg2.png' }
      ];
    } else if ((inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Single Leg').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Cut Length').toLowerCase()) > -1) ||
      (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye and Eye').toLowerCase()) > -1) || (inspectionHeader.PRODUCT_CONFIG.toLowerCase().indexOf(('Eye One End').toLowerCase()) > -1)) {
      this.configLocationOptions = ["Leg 1"]
      this.configlegOptionsList = [
        { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
      ];
    } else {
      this.configLocationOptions = ["Leg 1"]
      this.configlegOptionsList = [
        { item: 'Grommet Leg 1', img: './assets/img/GrommetLeg1.png' }
      ];
    }

  }

  getConfigLocationOptions() {
    return this.configLocationOptions;
  }

  getConfigLegOptions() {
    return this.configlegOptionsList;
  }

  async setLoadBearingOption(inspectionHeader, layer) {
    if (inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid').toLowerCase()) > -1) {
      if (inspectionHeader.HAS_CHAFE == 1) {
        if (layer == 'Chafe') {
          this.loadBearingOption = 'nonLoad'
        } else {
          this.loadBearingOption = 'load'
        }
      } else {
        this.loadBearingOption = 'load'
      }
    } else {
      if (inspectionHeader.IS_JACKETED == 1) {
        if (inspectionHeader.HAS_CHAFE == 1) {
          if (layer == 'Chafe') {
            this.loadBearingOption = 'nonLoad'
          } else if (layer == 'Rope Cover') {
            this.loadBearingOption = 'nonLoad'
          } else {
            this.loadBearingOption = 'load'
          }
        } else {
          if (layer == 'Rope Cover') {
            this.loadBearingOption = 'nonLoad'
          } else {
            this.loadBearingOption = 'load'
          }
        }
      } else {
        if (inspectionHeader.HAS_CHAFE == 1) {
          if (layer == 'Chafe') {
            this.loadBearingOption = 'nonLoad'
          } else {
            this.loadBearingOption = 'load'
          }
        } else {
          this.loadBearingOption = 'load'
        }
      }
    }

  }

  getLoadBearingOption() {
    return this.loadBearingOption
  }

  writeLog() {
    this.unviredCordovaSDK.logError("Measurement", "take measurement", "measurement process started")
  }

  witeResult(json) {
    this.unviredCordovaSDK.logError("Measurement", "take measurement", "measurement process completed RESULT : " + JSON.stringify(json))
  }

  setSelectedInspectionType(inspectionType) {
    this.selectedInspectionType = inspectionType
  }

  getSelectedInspectionType() {
    return this.selectedInspectionType;
  }

  setApplicationSetting(segment) {
    var settingHeader = new APP_SETTINGS_HEADER();
    settingHeader.KEY_FLD = 'SelectedSegment';
    settingHeader.VALUE = segment;
    this.selectedSegment = segment;
    this.unviredCordovaSDK.dbInsertOrUpdate("APP_SETTINGS_HEADER", settingHeader, true)
  }

  async getApplicationSetting() {
    var tempResult = await this.unviredCordovaSDK.dbSelect("APP_SETTINGS_HEADER", "KEY_FLD like 'SelectedSegment'")
    if (tempResult.type == ResultType.success) {
      if (tempResult.data.length > 0) {
        if (tempResult.data[0].VALUE != '') {
          this.selectedSegment = tempResult.data[0].VALUE;
        } else {
          this.selectedSegment = 'length'
        }
      } else {
        this.selectedSegment = 'length'
      }
    } else {
      this.selectedSegment = 'length'
    }
  }

  // async getMasterData() {
  //   return await this.unviredCordovaSDK.syncBackground(RequestType.QUERY, "", "" , "ROPE_INSPECTIONS_PA_GET_CUSTOMIZATION", "", "", false)
  // }

  showEmail(flag) {
    if (flag == true) {
      this.showEmailFlag = flag;
    } else {
      this.showEmailFlag = false;
    }
  }

  async navigateToLineTracker(page) {
    this.setConfigurationOption()
    this.setLastUsedAccount();
    this.setLastUsedAsset();
    this.setLastUsedWorkOrder();
    var loggedinUserRes = await this.unviredCordovaSDK.dbSelect(AppConstant.TABLE_USER_HEADER, '')
    if (loggedinUserRes.type == ResultType.success) {
      if (loggedinUserRes.data.length > 0) {
        if (loggedinUserRes.data[0].LINE_TRACKER_TYPE != '' && loggedinUserRes.data[0].LINE_TRACKER_TYPE != null && loggedinUserRes.data[0].LINE_TRACKER_TYPE != undefined) {
          // this.router.navigate(['line-tracker-home']);
          if (this.platform.is("ios") || this.platform.is("ipad") || this.platform.is("iphone")) {
            this.navCtrl.setDirection('root');
            this.navCtrl.navigateRoot(['home'])
            setTimeout(() => {
              this.router.navigate(['line-tracker-home']);
            }, 100);
          } else {
            this.navCtrl.navigateRoot('/line-tracker-home')
          }
        } else {
          this.showAlert()
          // this.router.navigate(['line-tracker-home']);
        }
      } else {
        this.unviredCordovaSDK.logError("dataService", "NavigateToLineTracker", "LoggedInUser not found" + JSON.stringify(loggedinUserRes))
        this.showAlert()
        // this.router.navigate(['line-tracker-home']);
      }
    } else {
      this.unviredCordovaSDK.logError("dataService", "NavigateToLineTracker", "LoggedInUser not found" + JSON.stringify(loggedinUserRes))
      this.showAlert()
      // this.router.navigate(['line-tracker-home']);
    }
  }

  async gotoGuestInspections() {
    const alert = await this.alertController.create({
      message: this.translate.instant("GUEST_MESSAGE"),
      buttons: ['OK']
    });
    await alert.present();
  }

  gotoContact() {
    // var iab = this.iab.create("https://www.samsonrope.com/contact/samson-rope-contacts", "_system");
    // if (iab != null) {
    //   iab.show();
    // }
    this.router.navigate(['contact'])
  }

  gotoResources() {
    // var iab = this.iab.create("https://www.samsonrope.com/resources", "_system");
    // if (iab != null) {
    //   iab.show();
    // }
    this.openUrlInBrowser("Resources");
  }

  async openUrlInBrowser(page) {
    var url = "";
    let uri_dec;
    this.alertService.present();    
      var inputHeader = {
        PAGE_NAME: page
      }
      let inputObject = {
        "INPUT_GET_CONNECT_URL": [
          {
            "INPUT_GET_CONNECT_URL_HEADER": inputHeader
          }]
      }
      var temp = await this.unviredCordovaSDK.syncForeground(RequestType.QUERY, "", inputObject, "ROPE_INSPECTIONS_PA_GET_CONNECT_URL", false)
      // console.log("SYNC Result " + JSON.stringify(temp))
      if (temp.type == ResultType.error) {
        this.alertService.showAlert("Error", temp.message)
        this.unviredCordovaSDK.logError("Contact", "send message ", JSON.stringify(temp))
        this.alertService.dismiss(); 
      } else {
        this.alertService.dismiss();
        let decodeString;
        let decodedJson;
        if(this.device.platform=='browser') {
          decodeString = atob(temp.data.data)
          decodedJson = JSON.parse(decodeString)
          uri_dec = decodeURIComponent(decodedJson.pageURL);
          // uri_dec = (url);
        } else {
          url = temp.data.url
          // console.log(temp)
          decodeString = atob(temp.data)
          decodedJson = JSON.parse(decodeString)
          url = decodedJson.pageURL;
          uri_dec = decodeURIComponent(url);
        }
        
        
      }
   const browser = this.iab.create(uri_dec, "_system", "");
    if (browser != null) {
      this.unlockScreen();
      browser.show();
    }
    browser.on('exit').subscribe(event => {
         console.log("exit " +  event)
         this.setPortrait()
    });
    browser.on('loadstop').subscribe(event => {
      console.log("loadstop " +  event)
    });
    browser.on('loaderror').subscribe(event => {
      console.log("loaderror " +  event)
    });
    browser.on('loadstart').subscribe(event => {
      // browser.executeScript({code: " window.localStorage.setItem('$AuraClientService.token$', 'eyJub25jZSI6InVvN1NMVkV0MVQxSU9ZaXlTLVZybFF2MGlQcS03emI2bkt2c0dOMXFkTVVcdTAwM2QiLCJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiIsImtpZCI6IntcInRcIjpcIjAwRDU0MDAwMDAwOGFKN1wiLFwidlwiOlwiMDJHNTQwMDAwMDAwdTg5XCIsXCJhXCI6XCJjYWltYW5zaWduZXJcIn0iLCJjcml0IjpbImlhdCJdLCJpYXQiOjE2MTY0NzU0NDMyODQsImV4cCI6MH0=..7IAPsNDT-FZcnx-ssqWw5O1H_yGfLxtTKHQ1b4D1uxU=');console.log('execute script')"}).then( function(){
      //   setTimeout(function(){
      //     browser.show();
      //   ;}, 1500);
      // }); 
      console.log("loadstart " +  event)
    });
  }

  public async navigateToHome() {
    this.navCtrl.navigateRoot('/home')
  }

  public async navigateToContact() {
    if(this.device.platform == "browser") {
      this.gotoContact();
      return;
    }
    if (this.platform.is("ios") || this.platform.is("ipad") || this.platform.is("iphone")) {
      this.navCtrl.setDirection('root');
      this.navCtrl.navigateRoot(['home'])
      setTimeout(() => {
        this.router.navigate(['contact']);
      }, 100);
    } else {
      this.navCtrl.navigateRoot('/contact')
    }
  }

  public async navigateToResources() {
    if(this.device.platform == "browser") {
      // this.gotoResources();
      this.openUrlInBrowser("Resources");
      return;
    }
    if (this.platform.is("ios") || this.platform.is("ipad") || this.platform.is("iphone")) {
      this.navCtrl.setDirection('root');
      this.navCtrl.navigateRoot(['home'])
      setTimeout(() => {
        this.router.navigate(['resource']);
      }, 100);
    } else {
      this.navCtrl.navigateRoot('/resource')
    }
  }

  public async navigateToInspection() {
    if (this.platform.is("ios") || this.platform.is("ipad") || this.platform.is("iphone")) {
      this.navCtrl.setDirection('root');
      this.navCtrl.navigateRoot(['home'])
      setTimeout(() => {
        this.router.navigate(['detailed-routine-inspection']);
      }, 100);
    } else {
      this.navCtrl.navigateRoot('/detailed-routine-inspection')
    }
  }

  async showAlert() {
    const alert = await this.alertController.create({
      backdropDismiss: true,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('Warning'),
      message: this.translate.instant('You don\'t have access to this tab. Please contact Samson for details.'),
      buttons: [this.translate.instant('OK')]
    });
    await alert.present();
  }

  async showErrorAlert(message) {
    const alert = await this.alertController.create({
      backdropDismiss: true,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('Error'),
      message: message,
      buttons: [this.translate.instant('OK')]
    });
    await alert.present();
  }

  async showInfoAlert(message) {
    const alert = await this.alertController.create({
      backdropDismiss: true,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('info'),
      message: message,
      buttons: [this.translate.instant('OK')]
    });
    await alert.present();
  }

  async showDataMissingAlert(field) {
    const alert = await this.alertController.create({
      backdropDismiss: true,
      animated: true,
      mode: 'ios',
      keyboardClose: true,
      header: this.translate.instant('Warning'),
      message: this.translate.instant('The certificate is not complete ' + field + ' is missing please contact your administrator'),
      buttons: [this.translate.instant('OK')]
    });
    await alert.present();
  }

  async checkAndSetDefaultAccount() {
    var result = await this.unviredCordovaSDK.dbSelect("ACCOUNT_HEADER", "")
    if (result.type == ResultType.success) {
      if (result.data.length == 1) {
        var selectedAsset = await this.userPreferenceService.getUserPreference('account');
        if (selectedAsset == '' && selectedAsset == undefined && selectedAsset == null && selectedAsset == 'null') {
          this.userPreferenceService.dbInsertOrUpdate('account', JSON.stringify(result.data[0]));
          this.lastUsedAccount = result.data[0]
        }
      }
      this.checkAndSetDefaultAsset()
    }

  }

  async checkAndSetDefaultAsset() {
    if(this.lastUsedAccount != undefined && this.lastUsedAccount != '' && this.lastUsedAccount != null && this.lastUsedAccount != 'null') {
      var result = await this.unviredCordovaSDK.dbSelect("ASSET_HEADER", "ACNT_ID = '" + this.lastUsedAccount.ID + "'")
      if (result.type == ResultType.success) {
        if (result.data.length == 1) {
          var selectedAsset = await this.userPreferenceService.getUserPreference('asset');
          if (selectedAsset == '' || selectedAsset == undefined || selectedAsset == null || selectedAsset == 'null') {
            this.userPreferenceService.dbInsertOrUpdate('asset', JSON.stringify(result.data[0]));
            this.lastUsedAsset = result.data[0]
          }
        }
      }
    }
  }

  

  setNetworkStatus(param) {
    this.isConnectedToNetwork = param;
  }

  getNetworkStatus() {
    return this.isConnectedToNetwork;
  }

  unlockScreen(){
    // allow user rotate
    this.screenOrientation.unlock();
  }

  setPortrait(){
    // set to portrait
    this.screenOrientation.lock(this.screenOrientation.ORIENTATIONS.PORTRAIT);
  }

  // generateUUID(): string {
  //   const generatedUUID = uuid.v4();
  //   const updatedUUID = generatedUUID.replace(/-/g, '');
  //   return updatedUUID;
  // }

  async setLastUsedAccount(account?: any) {
    if(account == undefined) {
      if(this.lastUsedAccount == undefined) {
        var selectedAccount = await this.userPreferenceService.getUserPreference('account');
        if (selectedAccount != '' && selectedAccount != undefined && selectedAccount != null && selectedAccount != 'null') {
          this.lastUsedAccount = JSON.parse(selectedAccount)
        } else {
          this.lastUsedAccount = ''
        } 
      }
    } else {
      //set from preference change when selected in inspection.
      this.lastUsedAccount = account;
    }
  }

  async setLastUsedAsset(asset?: any) {
    if(asset == undefined) {
      if(this.lastUsedAccount == undefined) {
        var selectedAsset = await this.userPreferenceService.getUserPreference('asset');
        if (selectedAsset != '' && selectedAsset != undefined && selectedAsset != null && selectedAsset != 'null') {
          this.lastUsedAsset = JSON.parse(selectedAsset)
        } else {
          this.lastUsedAsset = ''
        } 
      }
    } else {
      //set from preference change when selected in inspection.
      this.lastUsedAsset = asset;
    }    
  }

  setLastUsedWorkOrder(workOrder?: any) {
    if(workOrder != undefined) {
      //set from preference change when selected in inspection.
      this.lastUsedWorkOrder = workOrder;
    }
  }

  setLastUsedWorkOrderSelection(workOrderSelection?: any) {
    if(workOrderSelection != undefined) {
      //set from preference change when selected in inspection.
      this.lastUsedWorkOrderSelection = workOrderSelection;
    } 
  }

  setLastUsedCustomWorkOrder(workOrder?: any) {
    if(workOrder != undefined) {
      //set from preference change when selected in inspection.
      this.lastUsedCustomWorkOrder = workOrder;
    } 
  }

  getLastUsedAccount() {
    //return last used value
    return this.lastUsedAccount
  }

  getLastUsedAsset() {
    //return last used value
    return this.lastUsedAsset
  }

  getLastUsedWorkOrder() {
    //return last used value
    return this.lastUsedWorkOrder
  }

  getLastUsedWorkOrderSelection() {
    return this.lastUsedWorkOrderSelection;
  }

  getLastUsedCustomWorkOrder() {  
    return this.lastUsedCustomWorkOrder;
  }

  async setConfigurationOption() {
    var selectedConfigurationOption = await this.userPreferenceService.getUserPreference('configurationOption');
    if (selectedConfigurationOption != '' && selectedConfigurationOption != undefined && selectedConfigurationOption != null && selectedConfigurationOption != 'null') {
      this.configurationOption = selectedConfigurationOption
    } else {
      this.configurationOption = ''
    }  
  }

  getConfigurationOption() {
    return this.configurationOption;
  }

  async copyAnomalies(createdInspection) {
    // Check from event date not there in db..
    var query = `Select INSPECTION_ID, CREATED_DATE from INSPECTION_HEADER  WHERE (INSPECTION_STATUS = 'Completed' OR INSPECTION_STATUS = 'Historical') AND CERTIFICATE_NUM = '${createdInspection.CERTIFICATE_NUM}' ORDER BY EXT_FLD2 DESC`
    console.log(query)
    // create date format is different in windows
    // if(this.device.platform == 'windows') {
    //   query = `Select INSPECTION_ID, CREATED_DATE from INSPECTION_HEADER  WHERE (INSPECTION_STATUS = 'Completed' OR INSPECTION_STATUS = 'Historical') AND CERTIFICATE_NUM = '${createdInspection.CERTIFICATE_NUM}' ORDER BY INSPECTION_NAME DESC`
    // }
    var previousInspection = await this.unviredCordovaSDK.dbExecuteStatement(query)
    if(previousInspection.type == ResultType.success) {
      if(previousInspection.data.length > 0) {
        console.log(previousInspection.data)
        console.log(`Select * FROM MEASUREMENT WHERE INSPECTION_ID = '${previousInspection.data[0].INSPECTION_ID}' ORDER BY MEAS_TIMESTAMP ASC`)
        var previousMeasurements = await this.unviredCordovaSDK.dbExecuteStatement(`Select * FROM MEASUREMENT WHERE INSPECTION_ID = '${previousInspection.data[0].INSPECTION_ID}' ORDER BY MEAS_TIMESTAMP ASC`)
        for(var x = 0; x < previousMeasurements.data.length; x++) {
          previousMeasurements.data.sort(function(a,b) {
            if(JSON.parse(a.DATA).CreatedDate != undefined && JSON.parse(b.DATA).CreatedDate != undefined) {
              var tempa = a;
              var tempb = b
              a = (moment(JSON.parse(a.DATA).CreatedDate).format('DD-MM-YYYY')).split('-').reverse().join('')
              b = (moment(JSON.parse(b.DATA).CreatedDate).format('DD-MM-YYYY')).split('-').reverse().join('')
              return a > b ? 1 : a < b ? -1 : a == b ? tempa.MEAS_TIMESTAMP > tempb.MEAS_TIMESTAMP ? 1 : tempa.MEAS_TIMESTAMP < tempb.MEAS_TIMESTAMP ? -1 : 0 : 0;
            } else {
              return 0;
            }
          });
        }
        for(var i = 0; i < previousMeasurements.data.length; i++) {

          var measurementJson = JSON.parse(previousMeasurements.data[i].DATA)
          measurementJson.id = UtilserviceService.guid()
          measurementJson.externalImage = [];
          measurementJson.originalImages = {};
          measurementJson.otherData.fieldSegment = 'length'

          console.log("********** INSPECTION CREATE 1 *****" + measurementJson +  " ** "+ i + "****" +  JSON.stringify(measurementJson));
          console.log(previousMeasurements.data[i])

          measurementJson.otherData.observationNotes = (measurementJson.otherData.observationNotes != undefined && measurementJson.otherData.observationNotes != null && measurementJson.otherData.observationNotes != "null" && measurementJson.otherData.observationNotes != '') ? measurementJson.otherData.observationNotes :''
          if(measurementJson.CreatedDate == undefined || measurementJson.CreatedDate == null || measurementJson.CreatedDate == '') {
            if(this.device.platform == "iOS") {
              measurementJson["CreatedDate"] = moment(createdInspection.CREATED_DATE).format('MM-DD-YYYY');
            } else {
              measurementJson["CreatedDate"] = moment(createdInspection.CREATED_DATE.slice(0, 10)).format('MM-DD-YYYY'); 
            }
          } else {
            if(this.device.platform == "iOS") {
              measurementJson.CreatedDate = measurementJson.CreatedDate; 
            } else {
              measurementJson.CreatedDate = moment(measurementJson.CreatedDate).format('MM-DD-YYYY'); 
            }
          }  

          console.log("********** Event Date 1***** " + measurementJson.EventDate+ " ** "+ measurementJson.EventDate )

          if(measurementJson.EventDate == undefined || measurementJson.EventDate == null || measurementJson.EventDate == '') {

            console.log("********** Event Date 2***** " + measurementJson.EventDate + " ** "+ measurementJson.EventDate )
            if(this.device.platform == "iOS") {
              var tempDate = new Date(createdInspection.INSPECTION_DATE)
              if(tempDate + '' == 'Invalid Date') {
                measurementJson["EventDate"] = createdInspection.INSPECTION_DATE
              } else {
                measurementJson["EventDate"] = moment(tempDate.toISOString()).format('MM-DD-YYYY')
              }
            } else {
              measurementJson["EventDate"] = moment(createdInspection.INSPECTION_DATE.slice(0, 10)).format('MM-DD-YYYY'); 
            }
            console.log("********** Event Date 3***** " + measurementJson["EventDate"] + " ** "+ measurementJson["EventDate"])
          } else {            
            if(this.device.platform == "iOS") {
              var tempDate = new Date(measurementJson.EventDate)
              if(tempDate + '' == 'Invalid Date') {
                measurementJson.EventDate = measurementJson.EventDate
              } else {
                measurementJson.EventDate = moment(tempDate.toISOString()).format('MM-DD-YYYY')
              }              
            } else {
              measurementJson.EventDate = moment(measurementJson.EventDate).format('MM-DD-YYYY'); 
            }

            console.log("********** Event Date 4 **** " +  measurementJson.EventDate + " ** "+ measurementJson.EventDate )
          }
          var inspectionHeader =  previousInspection.data[0]
          if (inspectionHeader.CONSTRUCTION != undefined && inspectionHeader.CONSTRUCTION != null && inspectionHeader.CONSTRUCTION.toLowerCase().indexOf(('Double Braid').toLowerCase()) > -1) {
            if (inspectionHeader.HAS_CHAFE == 1) {
              if (measurementJson.otherData.layerOptions == 'Chafe') {
                measurementJson.otherData.damageType = 'nonLoad'
              } else {
                measurementJson.otherData.damageType = 'load'
              }
            } else {
              measurementJson.otherData.damageType = 'load'
            }
          } else {
            if (inspectionHeader.IS_JACKETED == 1) {
              if (inspectionHeader.HAS_CHAFE == 1) {
                if (measurementJson.otherData.layerOptions == 'Chafe') {
                  measurementJson.otherData.damageType = 'nonLoad'
                } else if (measurementJson.otherData.layerOptions == 'Rope Cover') {
                  measurementJson.otherData.damageType = 'nonLoad'
                } else {
                  measurementJson.otherData.damageType = 'load'
                }
              } else {
                if (measurementJson.otherData.layerOptions == 'Rope Cover') {
                  measurementJson.otherData.damageType = 'nonLoad'
                } else {
                  measurementJson.otherData.damageType = 'load'
                }
              }
            } else {
              if (inspectionHeader.HAS_CHAFE == 1) {
                if (measurementJson.otherData.layerOptions == 'Chafe') {
                  measurementJson.otherData.damageType = 'nonLoad'
                } else {
                  measurementJson.otherData.damageType = 'load'
                }
              } else {
                measurementJson.otherData.damageType = 'load'
              }
            }
          }
      
          var jsonString = JSON.stringify(measurementJson);
          var measurementItem = new MEASUREMENT();
          measurementItem.DATA = jsonString;
          measurementItem.INSPECTION_ID = createdInspection.INSPECTION_ID;
          measurementItem.MEAS_ID = measurementJson.id;
          measurementItem.MEAS_TYPE_ID = measurementJson.type;
          measurementItem.FID = createdInspection.LID
          measurementItem.MEAS_TIMESTAMP = new Date().getTime();
          console.log(measurementItem)          
          var insertItem = await this.unviredCordovaSDK.dbInsertOrUpdate(AppConstant.TABLE_MEASUREMENT_ITEM, measurementItem, false)
        }
        // console.log(JSON.stringify(previousMeasurements))
      }
    }
  }

  setLoggedInUrl(url) {
    this.loggedinUrl = url;
  }

  getLoggedInUrl() {
    return this.loggedinUrl;
  }

  async initializeDataForCreateInspection() {
    console.log("test log 1")
    if(!(this.certLList &&  this.certLList.type == ResultType.success && this.certLList.data.length < 0)) {
      console.log("test log 2")
      if(this.lastUsedAccount != undefined && this.lastUsedAccount != '' && this.lastUsedAccount != null && this.lastUsedAccount != 'null' && this.lastUsedAsset != undefined && this.lastUsedAsset != null && this.lastUsedAsset != '' && this.lastUsedAsset != '""' && this.lastUsedAsset != 'null') {
        this.certLList = await this.unviredCordovaSDK.dbExecuteStatement(`Select ACCOUNT_ID, ASSET_ID, NAME, PRODUCT, DIAM, RPS, PRODUCT_SIZE_INCH, PRODUCT_SIZE_MM, PRODUCT_LEN_FEET, PRODUCT_LEN_METER,CERTIFICATE_NUM, RFT_NUM, MANUFACTURER, PRODUCT_TYPE, PRODUCT_CODE, PRODUCT_DESC, COLOR, COLOR_OTHER, CONSTRUCTION, INSPECTION_NOTES, OTHER_CHAFE, IS_JACKETED from CERTIFICATE_HEADER WHERE ASSET_ID = '${this.lastUsedAsset.ID}' AND ACCOUNT_ID = '${this.lastUsedAccount.ID}' ORDER BY NAME COLLATE NOCASE ASC`)
      } else  {
        this.certLList = [];
      }// await this.unviredCordovaSDK.dbExecuteStatement("Select ACCOUNT_ID, ASSET_ID, NAME, PRODUCT, DIAM, RPS, PRODUCT_SIZE_INCH, PRODUCT_SIZE_MM, PRODUCT_LEN_FEET, PRODUCT_LEN_METER,CERTIFICATE_NUM, RFT_NUM, MANUFACTURER, PRODUCT_TYPE, PRODUCT_CODE, PRODUCT_DESC, COLOR, COLOR_OTHER, CONSTRUCTION, INSPECTION_NOTES, OTHER_CHAFE, IS_JACKETED from CERTIFICATE_HEADER ORDER BY NAME COLLATE NOCASE ASC");
    }
    if(!(this.workorderList && this.workorderList.type == ResultType.success && this.workorderList.data.lenght < 0)) {
      if(this.lastUsedAccount != undefined && this.lastUsedAccount != '' && this.lastUsedAccount != null && this.lastUsedAccount != 'null' && this.lastUsedAsset != undefined && this.lastUsedAsset != null && this.lastUsedAsset != '' && this.lastUsedAsset != '""' && this.lastUsedAsset != 'null') {
        this.workorderList = await this.unviredCordovaSDK.dbExecuteStatement(`Select * FROM WORK_ORDER_HEADER WHERE ASSET_ID = '${this.lastUsedAsset.ID}' AND ACCOUNT = '${this.lastUsedAccount.ID}' ORDER BY WO_NUMBER COLLATE NOCASE ASC`)
      } else  {
        this.workorderList = await this.unviredCordovaSDK.dbExecuteStatement("Select * FROM WORK_ORDER_HEADER ORDER BY WO_NUMBER COLLATE NOCASE ASC")
      }
    }
    if(!(this.assetList && this.assetList.type == ResultType.success && this.assetList.data.length < 0)) {
      this.assetList = await this.unviredCordovaSDK.dbExecuteStatement("SELECT ID, NAME, ACNT_ID FROM ASSET_HEADER ORDER BY NAME COLLATE NOCASE ASC");
    }
    return;
  }

  async filterCert(accountId, AssetId) {
    // await this.alertService.present();
    if(accountId == this.accountId && AssetId == this.assetId) {
      if(!(this.certLList &&  this.certLList.type == ResultType.success && this.certLList.data.length < 0)) {
        this.certLList = await this.unviredCordovaSDK.dbExecuteStatement(`Select ACCOUNT_ID, ASSET_ID, NAME, PRODUCT, DIAM, RPS, PRODUCT_SIZE_INCH, PRODUCT_SIZE_MM, PRODUCT_LEN_FEET, PRODUCT_LEN_METER,CERTIFICATE_NUM, RFT_NUM, MANUFACTURER, PRODUCT_TYPE, PRODUCT_CODE, PRODUCT_DESC, COLOR, COLOR_OTHER, CONSTRUCTION, INSPECTION_NOTES, OTHER_CHAFE, IS_JACKETED from CERTIFICATE_HEADER WHERE ASSET_ID = '${AssetId}' AND ACCOUNT_ID = '${accountId}' ORDER BY NAME COLLATE NOCASE ASC`)
      } else {
      }
    } else {
      this.certLList = await this.unviredCordovaSDK.dbExecuteStatement(`Select ACCOUNT_ID, ASSET_ID, NAME, PRODUCT, DIAM, RPS, PRODUCT_SIZE_INCH, PRODUCT_SIZE_MM, PRODUCT_LEN_FEET, PRODUCT_LEN_METER,CERTIFICATE_NUM, RFT_NUM, MANUFACTURER, PRODUCT_TYPE, PRODUCT_CODE, PRODUCT_DESC, COLOR, COLOR_OTHER, CONSTRUCTION, INSPECTION_NOTES, OTHER_CHAFE, IS_JACKETED from CERTIFICATE_HEADER WHERE ASSET_ID = '${AssetId}' AND ACCOUNT_ID = '${accountId}' ORDER BY NAME COLLATE NOCASE ASC`)
    }
    // if(this.alertService.isLoading) {
    //   await this.alertService.dismiss();
    // }
  }

  async userEnabledForInsightAI() {
    let res = await this.unviredCordovaSDK.dbSelect('USER_HEADER',"INSIGHT_AI='X'");
    if(res.type == ResultType.success) {
      if(res.data.length > 0 ) {
          this.isUserEnabledForInsightAI = true;
          return this.isUserEnabledForInsightAI;
      } else {
        this.isUserEnabledForInsightAI = false;
          return this.isUserEnabledForInsightAI;
      }
    }
  }
}
