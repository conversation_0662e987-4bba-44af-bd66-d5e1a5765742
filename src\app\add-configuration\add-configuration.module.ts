import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { AddConfigurationPageRoutingModule } from './add-configuration-routing.module';

import { AddConfigurationPage } from './add-configuration.page';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    FontAwesomeModule,
    TranslateModule,
    AddConfigurationPageRoutingModule
  ],
  declarations: [AddConfigurationPage]
})
export class AddConfigurationPageModule {}
