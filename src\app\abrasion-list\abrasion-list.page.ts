import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NavParams, PopoverController } from '@ionic/angular';

@Component({
  selector: 'app-abrasion-list',
  templateUrl: './abrasion-list.page.html',
  styleUrls: ['./abrasion-list.page.scss'],
})
export class AbrasionListPage implements OnInit {

  observationList: any[] = []
  @Input() isTenex: boolean;
  
  constructor(public translate: TranslateService,
    private popoverController: PopoverController) {
  }

  ngOnInit() {
    this.observationList = [ { type: 'External Abrasion', typeValue: 'external'},
      { type: 'Internal Abrasion', typeValue: 'internal' },
      { type: 'Cut Strands', typeValue: 'cuts' },
      { type: 'Twist', typeValue: 'twist' },
      { type: 'Compression', typeValue: 'compression' },
      { type: 'Melting', typeValue: 'melting' },
      { type: 'Contamination', typeValue: 'contamination' },
      { type: 'Pulled Strands', typeValue: 'pulled' },
      { type: 'Discoloration', typeValue: 'discoloration' },
      { type: 'Inconsistent Diameter', typeValue: 'inconsistent diameter' },
      { type: 'Linear Damage', typeValue: 'linear damage' },
      { type: 'Kinked Yarns', typeValue: 'kinked yarns' },
      { type: 'Parting', typeValue: 'parting' }]
  
    // ! remove internal abraion from list if the selected rope cert is of Type Tenex
    if(this.isTenex) {
      this.observationList= this.observationList.filter((item) =>item.typeValue!='internal')
    }
  }

  close(index: any) {
    this.popoverController.dismiss(index);
  }

}
