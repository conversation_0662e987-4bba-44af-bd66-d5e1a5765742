const fs = require('fs');
const path = require('path');

// Function to remove all PDF files for Android platform
module.exports = function(context) {
    const assetsDir = path.join('platforms', 'android', 'app', 'src', 'main', 'assets', 'www', 'assets');

    fs.readdir(assetsDir, (err, files) => {
        if (err) {
            console.error('Error reading directory:', err);
            return;
        }

        // Iterate over files and remove PDF files
        files.forEach(file => {
            if (file.endsWith('.pdf')) {
                const filePath = path.join(assetsDir, file);

                // Remove the PDF file
                fs.unlink(filePath, err => {
                    if (err) {
                        console.error(`Error removing ${file}:`, err);
                    } else {
                        console.log(`Removed ${file}`);
                    }
                });
            }
        });
    });
}

